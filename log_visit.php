<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Configuración
$logFile = 'virus_logs.json';
$maxLogSize = 10 * 1024 * 1024; // 10MB máximo

// Función para obtener información adicional del servidor
function getServerInfo() {
    return [
        'server_time' => date('Y-m-d H:i:s'),
        'server_ip' => $_SERVER['SERVER_ADDR'] ?? 'unknown',
        'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
        'http_host' => $_SERVER['HTTP_HOST'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
        'remote_addr' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'http_x_forwarded_for' => $_SERVER['HTTP_X_FORWARDED_FOR'] ?? null,
        'http_x_real_ip' => $_SERVER['HTTP_X_REAL_IP'] ?? null,
        'http_cf_connecting_ip' => $_SERVER['HTTP_CF_CONNECTING_IP'] ?? null,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'http_referer' => $_SERVER['HTTP_REFERER'] ?? null,
        'http_accept_language' => $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? null,
        'http_accept_encoding' => $_SERVER['HTTP_ACCEPT_ENCODING'] ?? null,
        'http_connection' => $_SERVER['HTTP_CONNECTION'] ?? null,
        'request_time' => $_SERVER['REQUEST_TIME'] ?? time(),
        'request_time_float' => $_SERVER['REQUEST_TIME_FLOAT'] ?? microtime(true)
    ];
}

// Función para detectar información adicional
function detectAdditionalInfo($data) {
    $info = [];
    
    // Detectar tipo de dispositivo
    $userAgent = $data['userAgent'] ?? '';
    if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
        $info['device_type'] = 'Mobile';
    } elseif (preg_match('/Tablet/', $userAgent)) {
        $info['device_type'] = 'Tablet';
    } else {
        $info['device_type'] = 'Desktop';
    }
    
    // Detectar bot/crawler
    if (preg_match('/bot|crawler|spider|scraper/i', $userAgent)) {
        $info['is_bot'] = true;
        $info['threat_level'] = 'LOW';
    } else {
        $info['is_bot'] = false;
        $info['threat_level'] = 'HIGH';
    }
    
    // Detectar VPN/Proxy por headers
    $headers = getallheaders();
    $vpnHeaders = ['X-Forwarded-For', 'X-Real-IP', 'CF-Connecting-IP', 'X-Originating-IP'];
    $info['proxy_headers'] = [];
    
    foreach ($vpnHeaders as $header) {
        if (isset($headers[$header])) {
            $info['proxy_headers'][$header] = $headers[$header];
        }
    }
    
    // Calcular riesgo
    $riskScore = 0;
    if ($info['is_bot']) $riskScore += 10;
    if (!empty($info['proxy_headers'])) $riskScore += 30;
    if (empty($data['referrer']) || $data['referrer'] === 'Direct Access') $riskScore += 20;
    
    $info['risk_score'] = $riskScore;
    
    if ($riskScore >= 50) {
        $info['risk_level'] = 'CRITICAL';
    } elseif ($riskScore >= 30) {
        $info['risk_level'] = 'HIGH';
    } elseif ($riskScore >= 15) {
        $info['risk_level'] = 'MEDIUM';
    } else {
        $info['risk_level'] = 'LOW';
    }
    
    return $info;
}

// Función para obtener geolocalización por IP
function getGeoLocation($ip) {
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
        $geoData = @file_get_contents("http://ip-api.com/json/{$ip}");
        if ($geoData) {
            return json_decode($geoData, true);
        }
    }
    return null;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Leer datos del POST
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
        
        if (!$data) {
            throw new Exception('Invalid JSON data');
        }
        
        // Obtener IP real del visitante
        $realIP = $_SERVER['HTTP_CF_CONNECTING_IP'] ?? 
                  $_SERVER['HTTP_X_REAL_IP'] ?? 
                  $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
                  $_SERVER['REMOTE_ADDR'] ?? 
                  'unknown';
        
        // Si hay múltiples IPs en X-Forwarded-For, tomar la primera
        if (strpos($realIP, ',') !== false) {
            $realIP = trim(explode(',', $realIP)[0]);
        }
        
        // Crear registro completo
        $logEntry = [
            'id' => uniqid('virus_', true),
            'timestamp' => date('Y-m-d H:i:s'),
            'unix_timestamp' => time(),
            'client_data' => $data,
            'server_info' => getServerInfo(),
            'real_ip' => $realIP,
            'additional_info' => detectAdditionalInfo($data),
            'geo_location' => getGeoLocation($realIP),
            'session_id' => session_id() ?: uniqid('sess_'),
            'fingerprint' => md5($realIP . ($data['userAgent'] ?? '') . date('Y-m-d'))
        ];
        
        // Leer logs existentes
        $existingLogs = [];
        if (file_exists($logFile)) {
            $existingContent = file_get_contents($logFile);
            if ($existingContent) {
                $existingLogs = json_decode($existingContent, true) ?: [];
            }
        }
        
        // Agregar nuevo log
        $existingLogs[] = $logEntry;
        
        // Limitar tamaño del archivo
        if (count($existingLogs) > 1000) {
            $existingLogs = array_slice($existingLogs, -1000); // Mantener solo los últimos 1000
        }
        
        // Guardar logs
        $jsonData = json_encode($existingLogs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        
        // Verificar tamaño del archivo
        if (strlen($jsonData) > $maxLogSize) {
            // Si es muy grande, mantener solo los últimos 500 registros
            $existingLogs = array_slice($existingLogs, -500);
            $jsonData = json_encode($existingLogs, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        }
        
        if (file_put_contents($logFile, $jsonData, LOCK_EX) === false) {
            throw new Exception('Failed to write log file');
        }
        
        // Respuesta exitosa
        echo json_encode([
            'status' => 'success',
            'message' => 'Visit logged successfully',
            'visitor_id' => $logEntry['id'],
            'risk_level' => $logEntry['additional_info']['risk_level'],
            'timestamp' => $logEntry['timestamp']
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Method not allowed'
    ]);
}
?>
