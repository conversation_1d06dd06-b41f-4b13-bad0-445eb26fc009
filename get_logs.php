<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

$logFile = 'virus_logs.json';

function calculateStats($logs) {
    $stats = [
        'total_visits' => count($logs),
        'today_visits' => 0,
        'critical_threats' => 0,
        'unique_ips' => 0,
        'bot_visits' => 0,
        'vpn_users' => 0,
        'countries' => [],
        'browsers' => [],
        'devices' => []
    ];
    
    $uniqueIPs = [];
    $today = date('Y-m-d');
    
    foreach ($logs as $log) {
        // Visitas de hoy
        if (strpos($log['timestamp'], $today) === 0) {
            $stats['today_visits']++;
        }
        
        // Amenazas críticas
        if (isset($log['additional_info']['risk_level']) && 
            $log['additional_info']['risk_level'] === 'CRITICAL') {
            $stats['critical_threats']++;
        }
        
        // IPs únicas
        $ip = $log['real_ip'] ?? 'unknown';
        if (!in_array($ip, $uniqueIPs)) {
            $uniqueIPs[] = $ip;
        }
        
        // Bots
        if (isset($log['additional_info']['is_bot']) && $log['additional_info']['is_bot']) {
            $stats['bot_visits']++;
        }
        
        // VPN/Proxy
        if (isset($log['additional_info']['proxy_headers']) && 
            !empty($log['additional_info']['proxy_headers'])) {
            $stats['vpn_users']++;
        }
        
        // Países
        if (isset($log['geo_location']['country'])) {
            $country = $log['geo_location']['country'];
            $stats['countries'][$country] = ($stats['countries'][$country] ?? 0) + 1;
        }
        
        // Dispositivos
        if (isset($log['additional_info']['device_type'])) {
            $device = $log['additional_info']['device_type'];
            $stats['devices'][$device] = ($stats['devices'][$device] ?? 0) + 1;
        }
        
        // Navegadores (simplificado)
        if (isset($log['client_data']['userAgent'])) {
            $ua = $log['client_data']['userAgent'];
            $browser = 'Other';
            if (strpos($ua, 'Chrome') !== false) $browser = 'Chrome';
            elseif (strpos($ua, 'Firefox') !== false) $browser = 'Firefox';
            elseif (strpos($ua, 'Safari') !== false) $browser = 'Safari';
            elseif (strpos($ua, 'Edge') !== false) $browser = 'Edge';
            
            $stats['browsers'][$browser] = ($stats['browsers'][$browser] ?? 0) + 1;
        }
    }
    
    $stats['unique_ips'] = count($uniqueIPs);
    
    // Ordenar arrays por frecuencia
    arsort($stats['countries']);
    arsort($stats['browsers']);
    arsort($stats['devices']);
    
    return $stats;
}

try {
    if (!file_exists($logFile)) {
        echo json_encode([
            'status' => 'success',
            'logs' => [],
            'stats' => [
                'total_visits' => 0,
                'today_visits' => 0,
                'critical_threats' => 0,
                'unique_ips' => 0,
                'bot_visits' => 0,
                'vpn_users' => 0
            ],
            'message' => 'No logs found'
        ]);
        exit;
    }
    
    $logContent = file_get_contents($logFile);
    if (!$logContent) {
        throw new Exception('Unable to read log file');
    }
    
    $logs = json_decode($logContent, true);
    if (!$logs) {
        throw new Exception('Invalid JSON in log file');
    }
    
    // Filtros opcionales
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 100;
    $riskLevel = isset($_GET['risk_level']) ? $_GET['risk_level'] : null;
    $dateFrom = isset($_GET['date_from']) ? $_GET['date_from'] : null;
    $dateTo = isset($_GET['date_to']) ? $_GET['date_to'] : null;
    
    // Aplicar filtros
    $filteredLogs = $logs;
    
    if ($riskLevel) {
        $filteredLogs = array_filter($filteredLogs, function($log) use ($riskLevel) {
            return isset($log['additional_info']['risk_level']) && 
                   $log['additional_info']['risk_level'] === strtoupper($riskLevel);
        });
    }
    
    if ($dateFrom) {
        $filteredLogs = array_filter($filteredLogs, function($log) use ($dateFrom) {
            return $log['timestamp'] >= $dateFrom;
        });
    }
    
    if ($dateTo) {
        $filteredLogs = array_filter($filteredLogs, function($log) use ($dateTo) {
            return $log['timestamp'] <= $dateTo . ' 23:59:59';
        });
    }
    
    // Limitar resultados
    $filteredLogs = array_slice($filteredLogs, -$limit);
    
    // Calcular estadísticas
    $stats = calculateStats($logs); // Usar todos los logs para estadísticas
    
    echo json_encode([
        'status' => 'success',
        'logs' => array_values($filteredLogs),
        'stats' => $stats,
        'total_logs' => count($logs),
        'filtered_logs' => count($filteredLogs),
        'filters_applied' => [
            'limit' => $limit,
            'risk_level' => $riskLevel,
            'date_from' => $dateFrom,
            'date_to' => $dateTo
        ]
    ], JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
