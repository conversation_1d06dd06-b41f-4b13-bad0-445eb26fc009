<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIRUS LOGS - PANEL DE CONTROL</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700&family=Courier+Prime:wght@400;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #0a0a0a;
            color: #0f0;
            font-family: 'Orbitron', monospace;
            min-height: 100vh;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #0f0;
            border-radius: 10px;
            background: rgba(0, 255, 0, 0.05);
        }
        
        .header h1 {
            font-size: 2.5rem;
            color: #ff0040;
            text-shadow: 0 0 20px #ff0040;
            margin-bottom: 10px;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(0, 255, 0, 0.1);
            border: 1px solid #0f0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #ff0040;
            display: block;
        }
        
        .stat-label {
            font-size: 0.9rem;
            color: #0ff;
            margin-top: 5px;
        }
        
        .controls {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            background: transparent;
            border: 2px solid #0f0;
            color: #0f0;
            padding: 10px 20px;
            font-family: 'Orbitron', monospace;
            cursor: pointer;
            border-radius: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #0f0;
            color: #000;
            box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
        }
        
        .logs-container {
            background: rgba(0, 0, 0, 0.8);
            border: 1px solid #0f0;
            border-radius: 10px;
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .log-entry {
            background: rgba(0, 255, 0, 0.05);
            border: 1px solid #0f0;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            font-family: 'Courier Prime', monospace;
            font-size: 0.9rem;
        }
        
        .log-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 10px;
            border-bottom: 1px solid #0f0;
        }
        
        .log-id {
            color: #ff0040;
            font-weight: 700;
        }
        
        .log-timestamp {
            color: #0ff;
        }
        
        .risk-level {
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 0.8rem;
            font-weight: 700;
        }
        
        .risk-critical { background: #ff0040; color: #fff; }
        .risk-high { background: #ff4040; color: #fff; }
        .risk-medium { background: #ffaa00; color: #000; }
        .risk-low { background: #0f0; color: #000; }
        
        .log-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }
        
        .detail-item {
            background: rgba(0, 0, 0, 0.5);
            padding: 8px;
            border-radius: 3px;
            border-left: 3px solid #0f0;
        }
        
        .detail-label {
            color: #0ff;
            font-weight: 700;
            display: block;
            margin-bottom: 3px;
        }
        
        .detail-value {
            color: #0f0;
            word-break: break-all;
        }
        
        .no-logs {
            text-align: center;
            color: #ff0040;
            font-size: 1.2rem;
            padding: 50px;
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.8rem;
            }
            
            .stats {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .log-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .log-details {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🦠 VIRUS DETECTION LOGS 🦠</h1>
        <p>Panel de Control - Sistema de Monitoreo Activo</p>
    </div>
    
    <div class="stats" id="stats">
        <!-- Las estadísticas se cargarán aquí -->
    </div>
    
    <div class="controls">
        <button class="btn" onclick="refreshLogs()">🔄 Actualizar</button>
        <button class="btn" onclick="clearLogs()">🗑️ Limpiar Logs</button>
        <button class="btn" onclick="exportLogs()">📥 Exportar</button>
        <button class="btn" onclick="toggleAutoRefresh()">⏱️ Auto-Refresh</button>
    </div>
    
    <div class="logs-container" id="logsContainer">
        <div class="no-logs">Cargando logs...</div>
    </div>

    <script>
        let autoRefreshInterval = null;
        let autoRefreshEnabled = false;

        // Cargar logs al iniciar
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs();
        });

        // Función para cargar logs
        async function loadLogs() {
            try {
                const response = await fetch('get_logs.php');
                const data = await response.json();
                
                if (data.status === 'success') {
                    displayStats(data.stats);
                    displayLogs(data.logs);
                } else {
                    document.getElementById('logsContainer').innerHTML = 
                        '<div class="no-logs">Error al cargar logs: ' + data.message + '</div>';
                }
            } catch (error) {
                document.getElementById('logsContainer').innerHTML = 
                    '<div class="no-logs">Error de conexión: ' + error.message + '</div>';
            }
        }

        // Mostrar estadísticas
        function displayStats(stats) {
            const statsContainer = document.getElementById('stats');
            statsContainer.innerHTML = `
                <div class="stat-card">
                    <span class="stat-number">${stats.total_visits}</span>
                    <div class="stat-label">Total Visitas</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${stats.today_visits}</span>
                    <div class="stat-label">Hoy</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${stats.critical_threats}</span>
                    <div class="stat-label">Amenazas Críticas</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${stats.unique_ips}</span>
                    <div class="stat-label">IPs Únicas</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${stats.bot_visits}</span>
                    <div class="stat-label">Bots Detectados</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">${stats.vpn_users}</span>
                    <div class="stat-label">Usuarios VPN</div>
                </div>
            `;
        }

        // Mostrar logs
        function displayLogs(logs) {
            const container = document.getElementById('logsContainer');
            
            if (!logs || logs.length === 0) {
                container.innerHTML = '<div class="no-logs">No hay logs disponibles</div>';
                return;
            }

            let html = '';
            logs.reverse().forEach(log => {
                const riskClass = 'risk-' + log.additional_info.risk_level.toLowerCase();
                
                html += `
                    <div class="log-entry">
                        <div class="log-header">
                            <span class="log-id">${log.id}</span>
                            <span class="log-timestamp">${log.timestamp}</span>
                            <span class="risk-level ${riskClass}">${log.additional_info.risk_level}</span>
                        </div>
                        <div class="log-details">
                            <div class="detail-item">
                                <span class="detail-label">IP Address:</span>
                                <span class="detail-value">${log.real_ip}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Ubicación:</span>
                                <span class="detail-value">${log.geo_location ? 
                                    log.geo_location.city + ', ' + log.geo_location.country : 'Desconocida'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">ISP:</span>
                                <span class="detail-value">${log.geo_location ? log.geo_location.isp || 'Desconocido' : 'Desconocido'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Dispositivo:</span>
                                <span class="detail-value">${log.additional_info.device_type}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Navegador:</span>
                                <span class="detail-value">${log.client_data.userAgent ? 
                                    log.client_data.userAgent.substring(0, 50) + '...' : 'Desconocido'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Puntuación de Riesgo:</span>
                                <span class="detail-value">${log.additional_info.risk_score}/100</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Es Bot:</span>
                                <span class="detail-value">${log.additional_info.is_bot ? 'Sí' : 'No'}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Referrer:</span>
                                <span class="detail-value">${log.client_data.referrer || 'Acceso Directo'}</span>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        // Funciones de control
        function refreshLogs() {
            loadLogs();
        }

        function clearLogs() {
            if (confirm('¿Estás seguro de que quieres limpiar todos los logs?')) {
                fetch('clear_logs.php', { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            loadLogs();
                        } else {
                            alert('Error al limpiar logs: ' + data.message);
                        }
                    });
            }
        }

        function exportLogs() {
            window.open('export_logs.php', '_blank');
        }

        function toggleAutoRefresh() {
            if (autoRefreshEnabled) {
                clearInterval(autoRefreshInterval);
                autoRefreshEnabled = false;
                document.querySelector('button[onclick="toggleAutoRefresh()"]').textContent = '⏱️ Auto-Refresh';
            } else {
                autoRefreshInterval = setInterval(loadLogs, 5000); // Cada 5 segundos
                autoRefreshEnabled = true;
                document.querySelector('button[onclick="toggleAutoRefresh()"]').textContent = '⏹️ Stop Auto-Refresh';
            }
        }
    </script>
</body>
</html>
