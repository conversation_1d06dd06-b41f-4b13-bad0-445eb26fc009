# 🦠 Sistema de Simulación de Virus - Página Trampa

Este sistema simula una infección de virus dramática para cualquier visitante que acceda al sitio web.

## 📁 Archivos del Sistema

### Archivos Principales
- `matrix-warning.html` - Página principal de simulación de virus
- `index.html` - Redirección automática a la página de virus
- `.htaccess` - Configuración de Apache para redirecciones

### Sistema de Logs
- `log_visit.php` - Registra todas las visitas con información detallada
- `get_logs.php` - API para obtener los logs registrados
- `view_logs.php` - Panel de control para visualizar logs
- `clear_logs.php` - API para limpiar los logs
- `export_logs.php` - Exportar logs en diferentes formatos
- `virus_logs.json` - Archivo donde se almacenan los logs (se crea automáticamente)

## 🎭 Características de la Simulación

### Efectos Visuales
- ✅ Pantalla de carga con pasos de "infección"
- ✅ Efecto Matrix con lluvia de código
- ✅ Cursor personalizado pulsante
- ✅ Efectos de glitch y parpadeo
- ✅ Líneas de escaneo
- ✅ Parpadeo de pantalla

### Información Recopilada
- 📍 Dirección IP real del visitante
- 🌍 Geolocalización (país, ciudad)
- 🏢 Proveedor de Internet (ISP)
- 💻 Información del dispositivo y navegador
- 🔍 Detección de VPN/Proxy
- 🤖 Detección de bots/crawlers
- ⏰ Timestamp completo de la visita
- 🔗 Referrer y User Agent

### Secuencia de "Ataque"
1. **Carga inicial** (10 pasos de "infección")
2. **Extracción de datos** del visitante
3. **Proceso de "infección"** (8 pasos)
4. **Cuenta regresiva** de 15 segundos
5. **Apertura masiva** de 50 pestañas con sitios extraños

## 🛠️ Instalación

1. Subir todos los archivos al servidor web
2. Asegurar que PHP esté habilitado
3. Dar permisos de escritura al directorio para crear `virus_logs.json`
4. Configurar `.htaccess` si es necesario

## 📊 Panel de Control

Accede a `view_logs.php` para ver:
- 📈 Estadísticas en tiempo real
- 📋 Lista detallada de todas las visitas
- 🔄 Actualización automática
- 📥 Exportación en múltiples formatos (JSON, CSV, XML, TXT)
- 🗑️ Limpieza de logs

### Estadísticas Disponibles
- Total de visitas
- Visitas del día actual
- Amenazas críticas detectadas
- IPs únicas
- Bots detectados
- Usuarios con VPN

## 🔧 Configuración

### Modificar URLs de Pestañas
Edita el array `weirdSites` en `matrix-warning.html` para cambiar las páginas que se abren.

### Ajustar Detección de Riesgo
Modifica la función `detectAdditionalInfo()` en `log_visit.php` para cambiar los criterios de riesgo.

### Personalizar Efectos
Cambia los CSS y JavaScript en `matrix-warning.html` para ajustar los efectos visuales.

## ⚠️ Advertencias Legales

- Este sistema es solo para fines educativos y de demostración
- No recopila información personal sensible
- No instala software malicioso real
- Asegúrate de cumplir con las leyes locales de privacidad
- Considera agregar un aviso de privacidad si es necesario

## 🎯 Sitios Web Incluidos para Apertura Masiva

El sistema incluye una lista de sitios web inofensivos pero extraños:
- Rick Roll de YouTube
- Zombo.com
- Staggering Beauty
- Koalas to the Max
- Cat Bounce
- Pointer Pointer
- Y muchos más...

## 📱 Compatibilidad

- ✅ Funciona en todos los navegadores modernos
- ✅ Responsive para móviles y tablets
- ✅ Compatible con Apache y Nginx
- ✅ Requiere PHP 7.0 o superior

## 🔒 Seguridad

- Los logs se almacenan localmente en el servidor
- No se envían datos a terceros
- Protección contra inyección SQL en los parámetros
- Validación de entrada en todos los endpoints

## 🚀 Uso

1. Cualquier visitante que acceda al sitio será redirigido automáticamente
2. La simulación comenzará inmediatamente
3. Todos los datos se registrarán automáticamente
4. Después de 15 segundos, se abrirán múltiples pestañas
5. Los administradores pueden ver los logs en `view_logs.php`

¡Disfruta asustando a los visitantes! 👻
