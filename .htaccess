# ========================================
# PROTECCIÓN AVANZADA CONTRA VULNERABILIDADES
# Sistema de Seguridad Matrix v2.0
# ========================================

# Deshabilitar listado de directorios
Options -Indexes -ExecCGI -FollowSymLinks

# Proteger archivos sensibles y críticos
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql|conf|config|env|git|svn|old|tmp|backup)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acceso a archivos ocultos y de sistema
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Proteger archivos PHP de configuración
<FilesMatch "(config|setup|install|admin|database|db-config)\.php$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# ========================================
# SISTEMA DE DETECCIÓN Y BLOQUEO MATRIX
# ========================================

RewriteEngine On

# ========================================
# REDIRECCIÓN UNIVERSAL A PÁGINA DE VIRUS
# ========================================

# Permitir acceso a archivos del sistema de logs (solo para administradores)
RewriteCond %{REQUEST_URI} ^/(view_logs|get_logs|log_visit|clear_logs|export_logs)\.php$ [OR]
RewriteCond %{REQUEST_URI} ^/virus_logs.*\.json$
RewriteRule .* - [L]

# Redireccionar TODOS los demás accesos a la página de virus
RewriteCond %{REQUEST_URI} !^/matrix-warning\.html$
RewriteRule .* /matrix-warning.html [R=302,L]

# ========================================
# NOTA: Las reglas de detección específicas han sido
# reemplazadas por la redirección universal arriba.
# Todos los accesos ahora van a la página de virus.
# ========================================

# ========================================
# RATE LIMITING AVANZADO
# ========================================

# Limitar requests por IP (requiere mod_evasive)
<IfModule mod_evasive24.c>
    DOSHashTableSize    3097
    DOSPageCount        2
    DOSPageInterval     1
    DOSBurstRequests    3
    DOSLogDir           "/var/log/apache2"
    DOSEmailNotify      <EMAIL>
    DOSSystemCommand    "iptables -A INPUT -s %s -j DROP"
</IfModule>

# Limitar tamaño de archivos y requests
LimitRequestBody 52428800
LimitRequestFields 50
LimitRequestFieldSize 8190
LimitRequestLine 8190

# Timeouts de seguridad
Timeout 60
KeepAliveTimeout 15

# ========================================
# HEADERS DE SEGURIDAD AVANZADOS
# ========================================

<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Protección XSS avanzada
    Header always set X-XSS-Protection "1; mode=block"
    
    # Prevenir MIME sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Política de referrer estricta
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy estricta
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self'; media-src 'self'; object-src 'none'; child-src 'none'; frame-ancestors 'none'; form-action 'self'; base-uri 'self';"
    
    # Strict Transport Security
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), speaker=(), vibrate=(), gyroscope=(), accelerometer=(), magnetometer=(), payment=(), usb=()"
    
    # Ocultar información del servidor
    Header unset Server
    Header unset X-Powered-By
    Header unset X-CF-Powered-By
    Header unset X-Mod-Pagespeed
    ServerTokens Prod
    
    # Headers personalizados de advertencia
    Header always set X-Security-Status "MONITORED"
    Header always set X-Intrusion-Detection "ACTIVE"
</IfModule>

# ========================================
# LOGGING AVANZADO DE SEGURIDAD
# ========================================

# Variables de entorno para logging
RewriteCond %{HTTP_USER_AGENT} (bot|spider|crawler|scan|hack|exploit) [NC]
RewriteRule .* - [E=SECURITY_THREAT:malicious_bot,L]

RewriteCond %{QUERY_STRING} (union.*select|script.*>|<.*script|drop.*table) [NC]
RewriteRule .* - [E=SECURITY_THREAT:injection_attempt,L]

RewriteCond %{REQUEST_URI} (\.\./|etc/passwd|proc/|dev/) [NC]
RewriteRule .* - [E=SECURITY_THREAT:directory_traversal,L]

# Formato de log personalizado para seguridad
LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" \"%{SECURITY_THREAT}e\" \"%{GEOIP_COUNTRY_CODE}e\"" security

# ========================================
# BLOQUEO GEOGRÁFICO (OPCIONAL)
# ========================================

# Bloquear países de alto riesgo (descomenta si es necesario)
# RewriteCond %{ENV:GEOIP_COUNTRY_CODE} ^(CN|RU|KP|IR|SY|AF)$
# RewriteRule .* /matrix-warning.html [R=302,L]

# ========================================
# PROTECCIÓN CONTRA HOTLINKING
# ========================================

RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?tudominio\.com [NC]
RewriteCond %{REQUEST_URI} \.(jpg|jpeg|png|gif|webp|pdf|zip)$ [NC]
RewriteRule .* - [F,L]

# ========================================
# CONFIGURACIONES ADICIONALES DE PHP
# ========================================

<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag register_globals Off
    php_flag magic_quotes_gpc Off
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_value post_max_size 8M
    php_value upload_max_filesize 8M
</IfModule>

# ========================================
# PROTECCIÓN FINAL
# ========================================

# Bloquear acceso directo a este archivo
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# Mensaje final en logs
RewriteRule ^.*$ - [E=MATRIX_SECURITY:SYSTEM_ACTIVE]