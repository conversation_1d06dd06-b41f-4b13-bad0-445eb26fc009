# ========================================
# PROTECCIÓN AVANZADA CONTRA VULNERABILIDADES
# Sistema de Seguridad Matrix v2.0
# ========================================

# Deshabilitar listado de directorios
Options -Indexes -ExecCGI -FollowSymLinks

# Proteger archivos sensibles y críticos
<FilesMatch "\.(htaccess|htpasswd|ini|log|sh|inc|bak|sql|conf|config|env|git|svn|old|tmp|backup)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Bloquear acceso a archivos ocultos y de sistema
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Proteger archivos PHP de configuración
<FilesMatch "(config|setup|install|admin|database|db-config)\.php$">
    Order Allow,<PERSON>y
    Deny from all
</FilesMatch>

# ========================================
# SISTEMA DE DETECCIÓN Y BLOQUEO MATRIX
# ========================================

RewriteEngine On

# Redireccionar a página de advertencia Matrix para accesos sospechosos
RewriteCond %{REQUEST_URI} !^/matrix-warning\.html$
RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
RewriteCond %{HTTP_USER_AGENT} (libwww-perl|wget|python|nikto|curl|scan|java|winhttp|clshttp|loader|masscan|nmap) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (;|<|>|'|"|\)|\(|%0A|%0D|%27|%3C|%3E|%00) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (Scrapy|scrapy|bot|Bot|BOT|spider|Spider|SPIDER|crawler|hack|exploit) [NC,OR]
RewriteCond %{HTTP_USER_AGENT} (sqlmap|burp|owasp|metasploit|kali|penetration) [NC]
RewriteRule .* /matrix-warning.html [R=302,L]

# Detectar intentos de inyección SQL y redirigir a Matrix
RewriteCond %{REQUEST_URI} !^/matrix-warning\.html$
RewriteCond %{QUERY_STRING} (\<|%3C).*script.*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} GLOBALS(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} _REQUEST(=|\[|\%[0-9A-Z]{0,2}) [OR]
RewriteCond %{QUERY_STRING} concat.*\( [NC,OR]
RewriteCond %{QUERY_STRING} union.*select.*\( [NC,OR]
RewriteCond %{QUERY_STRING} union.*all.*select.* [NC,OR]
RewriteCond %{QUERY_STRING} (drop|delete|insert|update).*table [NC,OR]
RewriteCond %{QUERY_STRING} \-[sdcr].*(allow_url_include|allow_url_fopen|safe_mode|disable_functions|auto_prepend_file) [NC]
RewriteRule .* /matrix-warning.html [R=302,L]

# Detectar XSS y redirigir a Matrix
RewriteCond %{REQUEST_URI} !^/matrix-warning\.html$
RewriteCond %{QUERY_STRING} (\<|%3C).*(script|iframe|object|embed).*(\>|%3E) [NC,OR]
RewriteCond %{QUERY_STRING} (javascript:|vbscript:|onload|onerror|onclick) [NC]
RewriteRule .* /matrix-warning.html [R=302,L]

# Detectar Directory Traversal y redirigir a Matrix
RewriteCond %{REQUEST_URI} !^/matrix-warning\.html$
RewriteCond %{QUERY_STRING} \.\./\.\./\.\./ [OR]
RewriteCond %{REQUEST_URI} (\.\./|\.\.\%2F|\.\.\%5C) [NC,OR]
RewriteCond %{REQUEST_URI} (etc/passwd|proc/|dev/|sys/|boot/|var/log) [NC]
RewriteRule .* /matrix-warning.html [R=302,L]

# Bloquear métodos HTTP peligrosos
RewriteCond %{REQUEST_METHOD} ^(TRACE|DELETE|TRACK|OPTIONS|PUT|PATCH) [NC]
RewriteRule .* /matrix-warning.html [R=302,L]

# Detectar intentos de acceso a directorios administrativos
RewriteCond %{REQUEST_URI} !^/matrix-warning\.html$
RewriteCond %{REQUEST_URI} ^/(admin|administrator|wp-admin|cpanel|phpmyadmin|mysql|database|config|includes|logs|backup|private|system|root|bin|usr|etc)/ [NC]
RewriteRule .* /matrix-warning.html [R=302,L]

# Detectar patrones de exploit comunes
RewriteCond %{REQUEST_URI} !^/matrix-warning\.html$
RewriteCond %{QUERY_STRING} (cmd=|exec=|passthru=|system=|shell_exec=|base64_decode|eval\() [NC,OR]
RewriteCond %{QUERY_STRING} (null|%00|%0d%0a|%0a|%0d|%09|%20script) [NC,OR]
RewriteCond %{QUERY_STRING} (<|>|'|%27|%3c|%3e|\"|%22) [NC]
RewriteRule .* /matrix-warning.html [R=302,L]

# ========================================
# RATE LIMITING AVANZADO
# ========================================

# Limitar requests por IP (requiere mod_evasive)
<IfModule mod_evasive24.c>
    DOSHashTableSize    3097
    DOSPageCount        2
    DOSPageInterval     1
    DOSBurstRequests    3
    DOSLogDir           "/var/log/apache2"
    DOSEmailNotify      <EMAIL>
    DOSSystemCommand    "iptables -A INPUT -s %s -j DROP"
</IfModule>

# Limitar tamaño de archivos y requests
LimitRequestBody 52428800
LimitRequestFields 50
LimitRequestFieldSize 8190
LimitRequestLine 8190

# Timeouts de seguridad
Timeout 60
KeepAliveTimeout 15

# ========================================
# HEADERS DE SEGURIDAD AVANZADOS
# ========================================

<IfModule mod_headers.c>
    # Prevenir clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Protección XSS avanzada
    Header always set X-XSS-Protection "1; mode=block"
    
    # Prevenir MIME sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Política de referrer estricta
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy estricta
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self'; media-src 'self'; object-src 'none'; child-src 'none'; frame-ancestors 'none'; form-action 'self'; base-uri 'self';"
    
    # Strict Transport Security
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
    
    # Permissions Policy
    Header always set Permissions-Policy "geolocation=(), microphone=(), camera=(), speaker=(), vibrate=(), gyroscope=(), accelerometer=(), magnetometer=(), payment=(), usb=()"
    
    # Ocultar información del servidor
    Header unset Server
    Header unset X-Powered-By
    Header unset X-CF-Powered-By
    Header unset X-Mod-Pagespeed
    ServerTokens Prod
    
    # Headers personalizados de advertencia
    Header always set X-Security-Status "MONITORED"
    Header always set X-Intrusion-Detection "ACTIVE"
</IfModule>

# ========================================
# LOGGING AVANZADO DE SEGURIDAD
# ========================================

# Variables de entorno para logging
RewriteCond %{HTTP_USER_AGENT} (bot|spider|crawler|scan|hack|exploit) [NC]
RewriteRule .* - [E=SECURITY_THREAT:malicious_bot,L]

RewriteCond %{QUERY_STRING} (union.*select|script.*>|<.*script|drop.*table) [NC]
RewriteRule .* - [E=SECURITY_THREAT:injection_attempt,L]

RewriteCond %{REQUEST_URI} (\.\./|etc/passwd|proc/|dev/) [NC]
RewriteRule .* - [E=SECURITY_THREAT:directory_traversal,L]

# Formato de log personalizado para seguridad
LogFormat "%h %l %u %t \"%r\" %>s %O \"%{Referer}i\" \"%{User-Agent}i\" \"%{SECURITY_THREAT}e\" \"%{GEOIP_COUNTRY_CODE}e\"" security

# ========================================
# BLOQUEO GEOGRÁFICO (OPCIONAL)
# ========================================

# Bloquear países de alto riesgo (descomenta si es necesario)
# RewriteCond %{ENV:GEOIP_COUNTRY_CODE} ^(CN|RU|KP|IR|SY|AF)$
# RewriteRule .* /matrix-warning.html [R=302,L]

# ========================================
# PROTECCIÓN CONTRA HOTLINKING
# ========================================

RewriteCond %{HTTP_REFERER} !^$
RewriteCond %{HTTP_REFERER} !^https?://(www\.)?tudominio\.com [NC]
RewriteCond %{REQUEST_URI} \.(jpg|jpeg|png|gif|webp|pdf|zip)$ [NC]
RewriteRule .* - [F,L]

# ========================================
# CONFIGURACIONES ADICIONALES DE PHP
# ========================================

<IfModule mod_php7.c>
    php_flag display_errors Off
    php_flag log_errors On
    php_flag register_globals Off
    php_flag magic_quotes_gpc Off
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
    php_value max_execution_time 30
    php_value max_input_time 30
    php_value memory_limit 128M
    php_value post_max_size 8M
    php_value upload_max_filesize 8M
</IfModule>

# ========================================
# PROTECCIÓN FINAL
# ========================================

# Bloquear acceso directo a este archivo
<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# Mensaje final en logs
RewriteRule ^.*$ - [E=MATRIX_SECURITY:SYSTEM_ACTIVE]