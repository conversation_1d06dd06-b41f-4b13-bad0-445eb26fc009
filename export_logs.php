<?php
$logFile = 'virus_logs.json';

// Determinar formato de exportación
$format = $_GET['format'] ?? 'json';
$filename = 'virus_logs_export_' . date('Y-m-d_H-i-s');

try {
    if (!file_exists($logFile)) {
        throw new Exception('Log file not found');
    }
    
    $logContent = file_get_contents($logFile);
    if (!$logContent) {
        throw new Exception('Unable to read log file');
    }
    
    $logs = json_decode($logContent, true);
    if (!$logs) {
        throw new Exception('Invalid JSON in log file');
    }
    
    switch ($format) {
        case 'csv':
            header('Content-Type: text/csv');
            header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
            
            $output = fopen('php://output', 'w');
            
            // Headers CSV
            $headers = [
                'ID', 'Timestamp', 'IP', 'Country', 'City', 'ISP', 'Device Type',
                'Browser', 'OS', 'Risk Level', 'Risk Score', 'Is Bot', 'User Agent'
            ];
            fputcsv($output, $headers);
            
            // Datos
            foreach ($logs as $log) {
                $row = [
                    $log['id'] ?? '',
                    $log['timestamp'] ?? '',
                    $log['real_ip'] ?? '',
                    $log['geo_location']['country'] ?? '',
                    $log['geo_location']['city'] ?? '',
                    $log['geo_location']['isp'] ?? '',
                    $log['additional_info']['device_type'] ?? '',
                    getBrowserFromUA($log['client_data']['userAgent'] ?? ''),
                    getOSFromUA($log['client_data']['userAgent'] ?? ''),
                    $log['additional_info']['risk_level'] ?? '',
                    $log['additional_info']['risk_score'] ?? '',
                    $log['additional_info']['is_bot'] ? 'Yes' : 'No',
                    substr($log['client_data']['userAgent'] ?? '', 0, 100)
                ];
                fputcsv($output, $row);
            }
            
            fclose($output);
            break;
            
        case 'txt':
            header('Content-Type: text/plain');
            header('Content-Disposition: attachment; filename="' . $filename . '.txt"');
            
            echo "VIRUS DETECTION LOGS EXPORT\n";
            echo "===========================\n";
            echo "Export Date: " . date('Y-m-d H:i:s') . "\n";
            echo "Total Records: " . count($logs) . "\n\n";
            
            foreach ($logs as $log) {
                echo "ID: " . ($log['id'] ?? 'N/A') . "\n";
                echo "Timestamp: " . ($log['timestamp'] ?? 'N/A') . "\n";
                echo "IP: " . ($log['real_ip'] ?? 'N/A') . "\n";
                echo "Location: " . ($log['geo_location']['city'] ?? 'Unknown') . ", " . ($log['geo_location']['country'] ?? 'Unknown') . "\n";
                echo "ISP: " . ($log['geo_location']['isp'] ?? 'Unknown') . "\n";
                echo "Device: " . ($log['additional_info']['device_type'] ?? 'Unknown') . "\n";
                echo "Risk Level: " . ($log['additional_info']['risk_level'] ?? 'Unknown') . "\n";
                echo "Risk Score: " . ($log['additional_info']['risk_score'] ?? 'N/A') . "\n";
                echo "Is Bot: " . (($log['additional_info']['is_bot'] ?? false) ? 'Yes' : 'No') . "\n";
                echo "User Agent: " . substr($log['client_data']['userAgent'] ?? 'Unknown', 0, 100) . "\n";
                echo "---\n\n";
            }
            break;
            
        case 'xml':
            header('Content-Type: application/xml');
            header('Content-Disposition: attachment; filename="' . $filename . '.xml"');
            
            echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
            echo '<virus_logs>' . "\n";
            echo '  <export_info>' . "\n";
            echo '    <export_date>' . date('Y-m-d H:i:s') . '</export_date>' . "\n";
            echo '    <total_records>' . count($logs) . '</total_records>' . "\n";
            echo '  </export_info>' . "\n";
            
            foreach ($logs as $log) {
                echo '  <log_entry>' . "\n";
                echo '    <id>' . htmlspecialchars($log['id'] ?? '') . '</id>' . "\n";
                echo '    <timestamp>' . htmlspecialchars($log['timestamp'] ?? '') . '</timestamp>' . "\n";
                echo '    <ip>' . htmlspecialchars($log['real_ip'] ?? '') . '</ip>' . "\n";
                echo '    <country>' . htmlspecialchars($log['geo_location']['country'] ?? '') . '</country>' . "\n";
                echo '    <city>' . htmlspecialchars($log['geo_location']['city'] ?? '') . '</city>' . "\n";
                echo '    <isp>' . htmlspecialchars($log['geo_location']['isp'] ?? '') . '</isp>' . "\n";
                echo '    <device_type>' . htmlspecialchars($log['additional_info']['device_type'] ?? '') . '</device_type>' . "\n";
                echo '    <risk_level>' . htmlspecialchars($log['additional_info']['risk_level'] ?? '') . '</risk_level>' . "\n";
                echo '    <risk_score>' . htmlspecialchars($log['additional_info']['risk_score'] ?? '') . '</risk_score>' . "\n";
                echo '    <is_bot>' . (($log['additional_info']['is_bot'] ?? false) ? 'true' : 'false') . '</is_bot>' . "\n";
                echo '    <user_agent>' . htmlspecialchars(substr($log['client_data']['userAgent'] ?? '', 0, 200)) . '</user_agent>' . "\n";
                echo '  </log_entry>' . "\n";
            }
            
            echo '</virus_logs>' . "\n";
            break;
            
        default: // JSON
            header('Content-Type: application/json');
            header('Content-Disposition: attachment; filename="' . $filename . '.json"');
            
            $exportData = [
                'export_info' => [
                    'export_date' => date('Y-m-d H:i:s'),
                    'total_records' => count($logs),
                    'format' => 'json'
                ],
                'logs' => $logs
            ];
            
            echo json_encode($exportData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    header('Content-Type: text/plain');
    echo 'Error exporting logs: ' . $e->getMessage();
}

function getBrowserFromUA($userAgent) {
    if (strpos($userAgent, 'Chrome') !== false) return 'Chrome';
    if (strpos($userAgent, 'Firefox') !== false) return 'Firefox';
    if (strpos($userAgent, 'Safari') !== false) return 'Safari';
    if (strpos($userAgent, 'Edge') !== false) return 'Edge';
    if (strpos($userAgent, 'Opera') !== false) return 'Opera';
    return 'Other';
}

function getOSFromUA($userAgent) {
    if (strpos($userAgent, 'Windows') !== false) return 'Windows';
    if (strpos($userAgent, 'Mac') !== false) return 'macOS';
    if (strpos($userAgent, 'Linux') !== false) return 'Linux';
    if (strpos($userAgent, 'Android') !== false) return 'Android';
    if (strpos($userAgent, 'iOS') !== false) return 'iOS';
    return 'Other';
}
?>
