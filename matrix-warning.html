<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ACCESO DENEGADO - SISTEMA DE SEGURIDAD MATRIX</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: #000;
            color: #0f0;
            font-family: 'Orbitron', monospace;
            overflow: hidden;
            height: 100vh;
            position: relative;
        }
        
        /* Efecto Matrix lluvia de código */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }
        
        .matrix-column {
            position: absolute;
            top: -100%;
            color: #0f0;
            font-size: 14px;
            line-height: 1.2;
            animation: matrixRain linear infinite;
        }
        
        @keyframes matrixRain {
            0% {
                transform: translateY(-100vh);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh);
                opacity: 0;
            }
        }
        
        /* Contenedor principal */
        .container {
            position: relative;
            z-index: 10;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
            padding: 20px;
        }
        
        /* Efecto de glitch en el título */
        .glitch {
            font-size: 3rem;
            font-weight: 900;
            text-transform: uppercase;
            position: relative;
            color: #0f0;
            letter-spacing: 5px;
            animation: glitch 2s infinite;
        }
        
        .glitch::before,
        .glitch::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .glitch::before {
            animation: glitch-1 0.3s infinite;
            color: #ff0040;
            z-index: -1;
        }
        
        .glitch::after {
            animation: glitch-2 0.3s infinite;
            color: #00ffff;
            z-index: -2;
        }
        
        @keyframes glitch {
            0%, 74%, 100% {
                transform: translate(0);
            }
            75% {
                transform: translate(2px, -2px);
            }
            76% {
                transform: translate(-2px, 2px);
            }
            77% {
                transform: translate(2px, -2px);
            }
        }
        
        @keyframes glitch-1 {
            0%, 74%, 100% {
                transform: translate(0);
            }
            75% {
                transform: translate(2px, -2px);
            }
        }
        
        @keyframes glitch-2 {
            0%, 74%, 100% {
                transform: translate(0);
            }
            75% {
                transform: translate(-2px, 2px);
            }
        }
        
        /* Información del intruso */
        .intrusion-info {
            background: rgba(0, 255, 0, 0.1);
            border: 2px solid #0f0;
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            max-width: 600px;
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.5);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% {
                box-shadow: 0 0 30px rgba(0, 255, 0, 0.5);
            }
            50% {
                box-shadow: 0 0 50px rgba(0, 255, 0, 0.8);
            }
        }
        
        .info-title {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ff0040;
            text-shadow: 0 0 10px #ff0040;
        }
        
        .info-item {
            margin: 10px 0;
            font-size: 1.1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .info-label {
            font-weight: 700;
            color: #0ff;
        }
        
        .info-value {
            color: #0f0;
            font-family: 'Courier New', monospace;
            background: rgba(0, 255, 0, 0.1);
            padding: 5px 10px;
            border-radius: 5px;
        }
        
        /* Mensaje de advertencia */
        .warning-message {
            font-size: 1.2rem;
            line-height: 1.6;
            margin: 20px 0;
            color: #ff0040;
            text-shadow: 0 0 10px #ff0040;
            animation: blink 1s infinite;
        }
        
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
        
        /* Contador regresivo */
        .countdown {
            font-size: 2rem;
            font-weight: 900;
            color: #ff0040;
            text-shadow: 0 0 20px #ff0040;
            margin: 20px 0;
        }
        
        /* Botón de escape (opcional) */
        .escape-btn {
            background: transparent;
            border: 2px solid #0f0;
            color: #0f0;
            padding: 15px 30px;
            font-family: 'Orbitron', monospace;
            font-size: 1rem;
            font-weight: 700;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 2px;
            transition: all 0.3s ease;
            margin-top: 20px;
        }
        
        .escape-btn:hover {
            background: #0f0;
            color: #000;
            box-shadow: 0 0 30px rgba(0, 255, 0, 0.8);
            transform: scale(1.05);
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .glitch {
                font-size: 2rem;
                letter-spacing: 2px;
            }
            
            .intrusion-info {
                padding: 20px;
                margin: 20px 10px;
            }
            
            .info-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 5px;
            }
            
            .warning-message {
                font-size: 1rem;
            }
        }
        
        /* Efectos adicionales */
        .scanline {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, transparent, #0f0, transparent);
            animation: scan 3s linear infinite;
        }
        
        @keyframes scan {
            0% { transform: translateY(0); }
            100% { transform: translateY(100vh); }
        }
    </style>
</head>
<body>
    <!-- Efecto Matrix de fondo -->
    <div class="matrix-bg" id="matrixBg"></div>
    
    <!-- Línea de escaneo -->
    <div class="scanline"></div>
    
    <div class="container">
        <h1 class="glitch" data-text="ACCESO DENEGADO">ACCESO DENEGADO</h1>
        
        <div class="intrusion-info">
            <div class="info-title">INTRUSIÓN DETECTADA - PROTOCOLO DE SEGURIDAD ACTIVADO</div>
            
            <div class="info-item">
                <span class="info-label">IP ADDRESS:</span>
                <span class="info-value" id="userIP">Obteniendo...</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">UBICACIÓN:</span>
                <span class="info-value" id="userLocation">Rastreando...</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">ISP:</span>
                <span class="info-value" id="userISP">Identificando...</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">USER AGENT:</span>
                <span class="info-value" id="userAgent">Analizando...</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">TIMESTAMP:</span>
                <span class="info-value" id="timestamp">Registrando...</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">THREAT LEVEL:</span>
                <span class="info-value" style="color: #ff0040; font-weight: bold;">CRÍTICO</span>
            </div>
        </div>
        
        <div class="warning-message">
            ⚠️ SU ACTIVIDAD SOSPECHOSA HA SIDO DETECTADA Y REGISTRADA ⚠️<br>
            TODAS LAS AUTORIDADES COMPETENTES HAN SIDO NOTIFICADAS<br>
            SU INFORMACIÓN PERSONAL Y DE RED HA SIDO DOCUMENTADA<br>
            CUALQUIER INTENTO ADICIONAL DE ACCESO SERÁ REPORTADO
        </div>
        
        <div class="countdown" id="countdown">REPORTANDO EN: 10</div>
        
        <button class="escape-btn" onclick="redirect()">SALIR DEL SISTEMA</button>
    </div>

    <script>
        // Generar efecto Matrix
        function createMatrixRain() {
            const matrixBg = document.getElementById('matrixBg');
            const chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZあいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわをん';
            
            for (let i = 0; i < 50; i++) {
                const column = document.createElement('div');
                column.className = 'matrix-column';
                column.style.left = Math.random() * 100 + '%';
                column.style.animationDuration = (Math.random() * 3 + 2) + 's';
                column.style.animationDelay = Math.random() * 2 + 's';
                
                let columnText = '';
                for (let j = 0; j < 20; j++) {
                    columnText += chars[Math.floor(Math.random() * chars.length)] + '<br>';
                }
                column.innerHTML = columnText;
                
                matrixBg.appendChild(column);
            }
        }
        
        // Obtener información del usuario
        async function getUserInfo() {
            try {
                // Mostrar User Agent
                document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 50) + '...';
                
                // Mostrar timestamp
                document.getElementById('timestamp').textContent = new Date().toISOString();
                
                // Intentar obtener IP y ubicación
                try {
                    const response = await fetch('https://ipapi.co/json/');
                    const data = await response.json();
                    
                    document.getElementById('userIP').textContent = data.ip || 'CLASIFICADO';
                    document.getElementById('userLocation').textContent = `${data.city || 'DESCONOCIDA'}, ${data.country_name || 'CLASIFICADO'}`;
                    document.getElementById('userISP').textContent = data.org || 'IDENTIFICANDO...';
                } catch (error) {
                    document.getElementById('userIP').textContent = 'PROTEGIDO';
                    document.getElementById('userLocation').textContent = 'UBICACIÓN CLASIFICADA';
                    document.getElementById('userISP').textContent = 'ISP NO IDENTIFICADO';
                }
            } catch (error) {
                console.log('Error al obtener información del usuario');
            }
        }
        
        // Contador regresivo
        function startCountdown() {
            let count = 10;
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(() => {
                count--;
                countdownElement.textContent = `REPORTANDO EN: ${count}`;
                
                if (count <= 0) {
                    clearInterval(timer);
                    countdownElement.textContent = 'INFORMACIÓN ENVIADA A AUTORIDADES';
                    countdownElement.style.color = '#ff0040';
                }
            }, 1000);
        }
        
        // Función de redirección
        function redirect() {
            // Redirigir a página principal o mostrar mensaje
            alert('Acceso denegado. Contacte al administrador del sistema.');
            window.location.href = '/';
        }
        
        // Inicializar efectos
        document.addEventListener('DOMContentLoaded', function() {
            createMatrixRain();
            getUserInfo();
            startCountdown();
            
            // Efecto de parpadeo aleatorio en el texto
            setInterval(() => {
                const glitch = document.querySelector('.glitch');
                glitch.style.textShadow = `
                    ${Math.random() * 4 - 2}px ${Math.random() * 4 - 2}px 0 #ff0040,
                    ${Math.random() * 4 - 2}px ${Math.random() * 4 - 2}px 0 #00ffff,
                    0 0 10px #0f0
                `;
            }, 100);
        });
        
        // Prevenir acciones del usuario
        document.addEventListener('keydown', function(e) {
            // Bloquear F12, Ctrl+Shift+I, etc.
            if (e.key === 'F12' || 
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u')) {
                e.preventDefault();
                return false;
            }
        });
        
        // Bloquear clic derecho
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });
        
        // Mensaje de consola para hackers
        console.log(`
        ███╗   ███╗ █████╗ ████████╗██████╗ ██╗██╗  ██╗
        ████╗ ████║██╔══██╗╚══██╔══╝██╔══██╗██║╚██╗██╔╝
        ██╔████╔██║███████║   ██║   ██████╔╝██║ ╚███╔╝ 
        ██║╚██╔╝██║██╔══██║   ██║   ██╔══██╗██║ ██╔██╗ 
        ██║ ╚═╝ ██║██║  ██║   ██║   ██║  ██║██║██╔╝ ██╗
        ╚═╝     ╚═╝╚═╝  ╚═╝   ╚═╝   ╚═╝  ╚═╝╚═╝╚═╝  ╚═╝
        
        SISTEMA DE SEGURIDAD MATRIX v2.0
        ================================
        Tu presencia ha sido detectada y registrada.
        Todos los intentos de acceso no autorizado son monitoreados.
        `);
    </script>
</body>
</html>