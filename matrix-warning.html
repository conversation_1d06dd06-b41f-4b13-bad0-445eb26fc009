<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>C:\Windows\System32\cmd.exe</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: #000;
            color: #0f0;
            font-family: 'Courier Prime', monospace;
            font-size: 14px;
            line-height: 1.2;
            overflow: hidden;
            height: 100vh;
            cursor: text;
        }

        .terminal {
            width: 100%;
            height: 100vh;
            padding: 10px;
            background: #000;
            color: #0f0;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .terminal::-webkit-scrollbar {
            width: 12px;
        }

        .terminal::-webkit-scrollbar-track {
            background: #000;
        }

        .terminal::-webkit-scrollbar-thumb {
            background: #0f0;
            border-radius: 6px;
        }

        .line {
            margin: 2px 0;
            display: block;
        }

        .prompt {
            color: #0f0;
        }

        .command {
            color: #fff;
        }

        .output {
            color: #0f0;
        }

        .error {
            color: #ff0040;
        }

        .warning {
            color: #ffaa00;
        }

        .info {
            color: #00ffff;
        }

        .cursor {
            background: #0f0;
            color: #000;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .typing {
            border-right: 2px solid #0f0;
            animation: typing 0.1s steps(1) infinite;
        }

        @keyframes typing {
            0% { border-color: #0f0; }
            50% { border-color: transparent; }
        }

        .hex-dump {
            color: #888;
            font-size: 12px;
        }

        .memory-addr {
            color: #0088ff;
        }

        .binary {
            color: #ff8800;
        }

        .success {
            color: #00ff00;
        }

        .critical {
            color: #ff0040;
            font-weight: bold;
            text-shadow: 0 0 5px #ff0040;
        }

        .progress-bar {
            display: inline-block;
        }

        .progress-char {
            color: #0f0;
        }

        .ip-info {
            color: #ffff00;
            font-weight: bold;
        }

        .hidden {
            display: none;
        }

        /* Efecto de parpadeo para texto crítico */
        .flash {
            animation: flash 0.5s infinite;
        }

        @keyframes flash {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="terminal" id="terminal"></div>

    <script>
        let terminal = document.getElementById('terminal');
        let currentLine = '';
        let lineIndex = 0;
        let userIP = '';
        let userCountry = '';
        let userISP = '';

        // Comandos y outputs que se ejecutarán
        const sequence = [
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'netstat -an | findstr ESTABLISHED', delay: 100 },
            { type: 'output', text: '  TCP    ***********05:49234    *************:443     ESTABLISHED\n  TCP    ***********05:49235    *************:443     ESTABLISHED\n  TCP    ***********05:49236    *************:443     ESTABLISHED', delay: 50 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'ipconfig /all', delay: 80 },
            { type: 'output', text: 'Configuración IP de Windows\n\nAdaptador de Ethernet Conexión de área local:\n   Sufijo DNS específico para la conexión. . : \n   Dirección IPv4. . . . . . . . . . . . . . : ***********05\n   Máscara de subred . . . . . . . . . . . . : *************\n   Puerta de enlace predeterminada . . . . . : ***********', delay: 30 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'nslookup myip.opendns.com resolver1.opendns.com', delay: 120 },
            { type: 'output', text: 'Servidor:  resolver1.opendns.com\nAddress:  **************\n\nRespuesta no autoritativa:\nNombre:    myip.opendns.com\nAddress:  ', delay: 40, dynamic: 'ip' },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'powershell -Command "Invoke-RestMethod -Uri \'http://ipinfo.io/json\'"', delay: 150 },
            { type: 'output', text: '{\n  "ip": "', delay: 30, dynamic: 'ip_start' },
            { type: 'output', text: '",\n  "hostname": "', delay: 20, dynamic: 'hostname' },
            { type: 'output', text: '",\n  "city": "', delay: 20, dynamic: 'city' },
            { type: 'output', text: '",\n  "region": "', delay: 20, dynamic: 'region' },
            { type: 'output', text: '",\n  "country": "', delay: 20, dynamic: 'country' },
            { type: 'output', text: '",\n  "org": "', delay: 20, dynamic: 'org' },
            { type: 'output', text: '",\n  "timezone": "America/New_York"\n}', delay: 30 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'reg query "HKLM\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion" /v ProductName', delay: 100 },
            { type: 'output', text: 'HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion\n    ProductName    REG_SZ    Windows 10 Pro', delay: 40 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'wmic computersystem get model,manufacturer', delay: 90 },
            { type: 'output', text: 'Manufacturer  Model\nDell Inc.     OptiPlex 7090', delay: 50 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'tasklist | findstr /i "chrome firefox edge safari"', delay: 80 },
            { type: 'output', text: 'chrome.exe                   3456 Console                    1    245,672 K\nchrome.exe                   4123 Console                    1     89,234 K\nchrome.exe                   4567 Console                    1    156,789 K', delay: 40 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'netsh wlan show profiles', delay: 70 },
            { type: 'output', text: 'Perfiles en la interfaz Wi-Fi:\n\nPerfiles de directiva de grupo (solo lectura)\n---------------------------------\n    <Ninguno>\n\nPerfiles de usuario\n-------------------\n    Todos los perfiles de usuario : WiFi_Network_5G\n    Todos los perfiles de usuario : HOME_NETWORK\n    Todos los perfiles de usuario : OFFICE_WIFI', delay: 30 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'dir C:\\Users\\<USER>\\Desktop /b', delay: 60 },
            { type: 'output', text: 'desktop.ini\nDocumentos importantes.docx\nFotos vacaciones.zip\nPasswords.txt\nBanco_Online.lnk\nCrypto_Wallet.exe', delay: 40 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'powershell -Command "Get-Process | Where-Object {$_.ProcessName -like \'*security*\' -or $_.ProcessName -like \'*antivirus*\' -or $_.ProcessName -like \'*defender*\'}"', delay: 120 },
            { type: 'output', text: 'Handles  NPM(K)    PM(K)      WS(K)     CPU(s)     Id  SI ProcessName\n-------  ------    -----      -----     ------     --  -- -----------\n    234      15     4567       8901       2.34   1234   1 SecurityHealthService\n    156       8     2345       4567       1.23   5678   1 MsMpEng', delay: 50 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'echo %COMPUTERNAME%', delay: 40 },
            { type: 'output', text: 'DESKTOP-7K9X2M1', delay: 20 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"', delay: 90 },
            { type: 'output', text: 'Nombre del sistema operativo:     Microsoft Windows 10 Pro\nVersión del sistema operativo:    10.0.19044 N/D Compilación 19044\nTipo de sistema:                   x64-based PC', delay: 40 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'powershell -Command "Get-WmiObject -Class Win32_VideoController | Select-Object Name"', delay: 100 },
            { type: 'output', text: 'Name\n----\nNVIDIA GeForce RTX 3070\nIntel(R) UHD Graphics 630', delay: 50 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'wmic memorychip get capacity,speed', delay: 70 },
            { type: 'output', text: 'Capacity     Speed\n17179869184  3200\n17179869184  3200', delay: 40 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'powershell -WindowStyle Hidden -Command "Start-Sleep 2; [System.Windows.Forms.SendKeys]::SendWait(\'{F11}\')"', delay: 200 },
            { type: 'output', text: '', delay: 10 },
            { type: 'prompt', text: 'C:\\Windows\\System32>' },
            { type: 'command', text: 'for /L %i in (1,1,200) do start "" "https://www.youtube.com/watch?v=dQw4w9WgXcQ"', delay: 300 },
            { type: 'output', text: '', delay: 10 }
        ];

        let sequenceIndex = 0;
        let charIndex = 0;
        let isTyping = false;

        // Función para obtener información del usuario
        async function getUserInfo() {
            try {
                const response = await fetch('https://ipapi.co/json/');
                const data = await response.json();

                userIP = data.ip || 'Unknown';
                userCountry = data.country_name || 'Unknown';
                userISP = data.org || 'Unknown';

                // Registrar visita
                logVisit(data);

            } catch (error) {
                userIP = 'Protected';
                userCountry = 'Hidden';
                userISP = 'Encrypted';
            }
        }

        // Función para registrar visita
        async function logVisit(ipData) {
            try {
                const visitData = {
                    timestamp: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    language: navigator.language,
                    platform: navigator.platform,
                    screenResolution: `${screen.width}x${screen.height}`,
                    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                    referrer: document.referrer || 'Direct Access',
                    url: window.location.href,
                    ipData: ipData
                };

                await fetch('log_visit.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(visitData)
                });
            } catch (error) {
                console.log('Error logging visit');
            }
        }

        // Función para escribir texto con efecto de tipeo
        function typeText(text, className = '', callback = null) {
            if (isTyping) return;
            isTyping = true;

            let span = document.createElement('span');
            span.className = className;
            terminal.appendChild(span);

            let i = 0;
            function typeChar() {
                if (i < text.length) {
                    span.textContent += text.charAt(i);
                    i++;
                    setTimeout(typeChar, Math.random() * 50 + 10);
                } else {
                    isTyping = false;
                    if (callback) callback();
                }
            }
            typeChar();
        }

        // Función para agregar nueva línea
        function addLine(text = '', className = '') {
            let div = document.createElement('div');
            div.className = 'line ' + className;
            div.textContent = text;
            terminal.appendChild(div);
            terminal.scrollTop = terminal.scrollHeight;
        }

        // Función para procesar la secuencia
        function processSequence() {
            if (sequenceIndex >= sequence.length) {
                // Al final, abrir pestañas
                setTimeout(openMultipleTabs, 2000);
                return;
            }

            let current = sequence[sequenceIndex];
            let delay = current.delay || 50;

            setTimeout(() => {
                switch (current.type) {
                    case 'prompt':
                        addLine(current.text, 'prompt');
                        break;

                    case 'command':
                        typeText(current.text, 'command', () => {
                            addLine('', '');
                            sequenceIndex++;
                            processSequence();
                        });
                        break;

                    case 'output':
                        let outputText = current.text;

                        // Reemplazar contenido dinámico
                        if (current.dynamic) {
                            switch (current.dynamic) {
                                case 'ip':
                                case 'ip_start':
                                    outputText += userIP;
                                    break;
                                case 'hostname':
                                    outputText += userISP.split(' ')[0] || 'unknown';
                                    break;
                                case 'city':
                                case 'region':
                                    outputText += 'Unknown';
                                    break;
                                case 'country':
                                    outputText += userCountry;
                                    break;
                                case 'org':
                                    outputText += userISP;
                                    break;
                            }
                        }

                        // Determinar clase CSS
                        let className = 'output';
                        if (current.dynamic === 'ip' || current.dynamic === 'ip_start' ||
                            current.dynamic === 'country' || current.dynamic === 'org') {
                            className += ' ip-info';
                        }

                        addLine(outputText, className);
                        break;
                }

                sequenceIndex++;
                processSequence();

            }, delay);
        }

        // Función para abrir múltiples pestañas
        function openMultipleTabs() {
            const sites = [
                'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
                'https://www.zombo.com/',
                'https://www.staggeringbeauty.com/',
                'https://www.koalastothemax.com/',
                'https://www.cat-bounce.com/',
                'https://www.pointer-pointer.com/',
                'https://www.omfgdogs.com/',
                'https://www.hackertyper.net/',
                'https://www.windows93.net/',
                'https://www.theuselessweb.com/',
                'https://www.randomcolour.com/',
                'https://www.ismycomputeron.com/',
                'https://www.ducksarethebest.com/',
                'https://www.beesbeesbees.com/',
                'https://www.endless.horse/',
                'https://www.fallingfalling.com/',
                'https://www.patience-is-a-virtue.org/',
                'https://www.republiquedesmangues.fr/',
                'https://www.cant-not-tweet-this.com/',
                'https://www.donothingfor2minutes.com/'
            ];

            let tabCount = 0;
            const maxTabs = 200;

            const tabInterval = setInterval(() => {
                if (tabCount < maxTabs) {
                    const randomSite = sites[Math.floor(Math.random() * sites.length)];
                    try {
                        window.open(randomSite, '_blank');
                    } catch (e) {
                        console.log('Popup blocked');
                    }
                    tabCount++;
                } else {
                    clearInterval(tabInterval);
                }
            }, 100);
        }
    </style>
</head>
<body>
    <!-- Cursor personalizado -->
    <div class="custom-cursor" id="customCursor"></div>

    <!-- Pantalla de carga inicial -->
    <div class="loading-screen" id="loadingScreen">
        <div class="virus-logo">☠️ VIRUS DETECTED ☠️</div>
        <div class="loading-text">INFILTRANDO SISTEMA...</div>
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
        <div class="loading-steps">
            <div class="step" id="step1">► Escaneando vulnerabilidades del sistema...</div>
            <div class="step" id="step2">► Insertando rastreador de ubicación...</div>
            <div class="step" id="step3">► Instalando keylogger...</div>
            <div class="step" id="step4">► Accediendo a cámara y micrófono...</div>
            <div class="step" id="step5">► Extrayendo datos personales...</div>
            <div class="step" id="step6">► Enviando información a servidor remoto...</div>
            <div class="step" id="step7">► Estableciendo backdoor permanente...</div>
            <div class="step" id="step8">► Deshabilitando antivirus...</div>
            <div class="step" id="step9">► Infectando archivos del sistema...</div>
            <div class="step" id="step10">► INFECCIÓN COMPLETADA</div>
        </div>
    </div>

    <!-- Efectos visuales -->
    <div class="matrix-bg" id="matrixBg"></div>
    <div class="scanline"></div>
    <div class="screen-flicker"></div>

    <div class="container" id="mainContainer" style="display: none;">
        <h1 class="glitch" data-text="SISTEMA COMPROMETIDO">SISTEMA COMPROMETIDO</h1>

        <div class="virus-info">
            <div class="virus-title">🦠 MALWARE ACTIVO - DATOS COMPROMETIDOS 🦠</div>

            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">IP ADDRESS:</span>
                    <span class="info-value" id="userIP">Extrayendo...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">UBICACIÓN GPS:</span>
                    <span class="info-value" id="userLocation">Triangulando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">ISP/PROVEEDOR:</span>
                    <span class="info-value" id="userISP">Identificando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">TIPO DE CONEXIÓN:</span>
                    <span class="info-value" id="connectionType">Analizando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">DISPOSITIVO:</span>
                    <span class="info-value" id="deviceInfo">Escaneando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">NAVEGADOR:</span>
                    <span class="info-value" id="browserInfo">Detectando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">SISTEMA OPERATIVO:</span>
                    <span class="info-value" id="osInfo">Identificando...</span>
                </div>

                <div class="info-item">
                    <span class="info-label">VPN/PROXY:</span>
                    <span class="info-value" id="vpnStatus">Verificando...</span>
                </div>
            </div>

            <div class="threat-level">⚠️ NIVEL DE AMENAZA: CRÍTICO ⚠️</div>
        </div>

        <div class="infection-process">
            <div class="process-title">PROCESO DE INFECCIÓN EN CURSO...</div>
            <div class="process-step" id="proc1">Instalando backdoor en puerto 4444...</div>
            <div class="process-step" id="proc2">Copiando archivos sensibles...</div>
            <div class="process-step" id="proc3">Enviando contraseñas guardadas...</div>
            <div class="process-step" id="proc4">Activando cámara web...</div>
            <div class="process-step" id="proc5">Grabando audio del micrófono...</div>
            <div class="process-step" id="proc6">Accediendo a historial de navegación...</div>
            <div class="process-step" id="proc7">Extrayendo datos bancarios...</div>
            <div class="process-step" id="proc8">Propagando virus a contactos...</div>
        </div>

        <div class="countdown" id="countdown">FINALIZANDO ATAQUE EN: 15</div>
    </div>

    <script>
        // Variables globales
        let userVisitData = {
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            language: navigator.language,
            platform: navigator.platform,
            cookieEnabled: navigator.cookieEnabled,
            onlineStatus: navigator.onLine,
            screenResolution: `${screen.width}x${screen.height}`,
            colorDepth: screen.colorDepth,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            referrer: document.referrer || 'Direct Access',
            url: window.location.href
        };

        // Cursor personalizado
        function initCustomCursor() {
            const cursor = document.getElementById('customCursor');
            document.addEventListener('mousemove', (e) => {
                cursor.style.left = e.clientX + 'px';
                cursor.style.top = e.clientY + 'px';
            });
        }

        // Generar efecto Matrix más intenso
        function createMatrixRain() {
            const matrixBg = document.getElementById('matrixBg');
            const chars = '01ハミヒーウシナモニサワツオリアホテマケメエカキムユラセネスタヌヘ';

            for (let i = 0; i < 80; i++) {
                const column = document.createElement('div');
                column.className = 'matrix-column';
                column.style.left = Math.random() * 100 + '%';
                column.style.animationDuration = (Math.random() * 2 + 1) + 's';
                column.style.animationDelay = Math.random() * 2 + 's';
                column.style.fontSize = (Math.random() * 8 + 10) + 'px';

                let columnText = '';
                for (let j = 0; j < 30; j++) {
                    columnText += chars[Math.floor(Math.random() * chars.length)] + '<br>';
                }
                column.innerHTML = columnText;

                matrixBg.appendChild(column);
            }
        }

        // Simulación de carga de virus
        function startVirusLoading() {
            const steps = [
                'step1', 'step2', 'step3', 'step4', 'step5',
                'step6', 'step7', 'step8', 'step9', 'step10'
            ];

            let currentStep = 0;
            let progress = 0;

            const loadingInterval = setInterval(() => {
                // Actualizar barra de progreso
                progress += Math.random() * 15 + 5;
                if (progress > 100) progress = 100;
                document.getElementById('progressFill').style.width = progress + '%';

                // Activar pasos
                if (currentStep < steps.length) {
                    // Completar paso anterior
                    if (currentStep > 0) {
                        document.getElementById(steps[currentStep - 1]).classList.remove('active');
                        document.getElementById(steps[currentStep - 1]).classList.add('completed');
                    }

                    // Activar paso actual
                    document.getElementById(steps[currentStep]).classList.add('active');
                    currentStep++;
                }

                // Finalizar carga
                if (progress >= 100 && currentStep >= steps.length) {
                    clearInterval(loadingInterval);
                    setTimeout(() => {
                        document.getElementById('loadingScreen').classList.add('hidden');
                        document.getElementById('mainContainer').style.display = 'flex';
                        startMainSequence();
                    }, 1000);
                }
            }, 800);
        }

        // Secuencia principal después de la carga
        function startMainSequence() {
            getUserInfo();
            startInfectionProcess();
            startFinalCountdown();
        }

        // Obtener información detallada del usuario
        async function getUserInfo() {
            try {
                // Información básica del navegador
                document.getElementById('deviceInfo').textContent = `${navigator.platform} - ${screen.width}x${screen.height}`;
                document.getElementById('browserInfo').textContent = getBrowserInfo();
                document.getElementById('osInfo').textContent = getOSInfo();

                // Intentar obtener información de IP y ubicación
                try {
                    const response = await fetch('https://ipapi.co/json/');
                    const data = await response.json();

                    userVisitData.ip = data.ip;
                    userVisitData.city = data.city;
                    userVisitData.country = data.country_name;
                    userVisitData.isp = data.org;
                    userVisitData.latitude = data.latitude;
                    userVisitData.longitude = data.longitude;

                    document.getElementById('userIP').textContent = data.ip || 'OCULTA';
                    document.getElementById('userLocation').textContent = `${data.city || 'DESCONOCIDA'}, ${data.country_name || 'CLASIFICADO'}`;
                    document.getElementById('userISP').textContent = data.org || 'PROVEEDOR DESCONOCIDO';
                    document.getElementById('connectionType').textContent = detectConnectionType(data);
                    document.getElementById('vpnStatus').textContent = detectVPN(data);

                } catch (error) {
                    document.getElementById('userIP').textContent = 'IP PROTEGIDA';
                    document.getElementById('userLocation').textContent = 'UBICACIÓN ENCRIPTADA';
                    document.getElementById('userISP').textContent = 'ISP BLOQUEADO';
                    document.getElementById('connectionType').textContent = 'CONEXIÓN SEGURA';
                    document.getElementById('vpnStatus').textContent = 'VPN DETECTADA';
                }

                // Registrar visita
                logVisit();

            } catch (error) {
                console.log('Error al obtener información del usuario');
            }
        }

        // Funciones auxiliares
        function getBrowserInfo() {
            const ua = navigator.userAgent;
            if (ua.includes('Chrome')) return 'Google Chrome';
            if (ua.includes('Firefox')) return 'Mozilla Firefox';
            if (ua.includes('Safari')) return 'Safari';
            if (ua.includes('Edge')) return 'Microsoft Edge';
            if (ua.includes('Opera')) return 'Opera';
            return 'Navegador Desconocido';
        }

        function getOSInfo() {
            const ua = navigator.userAgent;
            if (ua.includes('Windows')) return 'Windows';
            if (ua.includes('Mac')) return 'macOS';
            if (ua.includes('Linux')) return 'Linux';
            if (ua.includes('Android')) return 'Android';
            if (ua.includes('iOS')) return 'iOS';
            return 'Sistema Desconocido';
        }

        function detectConnectionType(data) {
            if (data.org && data.org.toLowerCase().includes('hosting')) return 'VPS/HOSTING';
            if (data.org && data.org.toLowerCase().includes('cloud')) return 'CLOUD SERVER';
            if (data.org && data.org.toLowerCase().includes('datacenter')) return 'DATACENTER';
            return 'CONEXIÓN RESIDENCIAL';
        }

        function detectVPN(data) {
            const vpnIndicators = ['vpn', 'proxy', 'tor', 'anonymous', 'private'];
            if (data.org) {
                const orgLower = data.org.toLowerCase();
                for (let indicator of vpnIndicators) {
                    if (orgLower.includes(indicator)) return 'VPN/PROXY DETECTADO';
                }
            }
            return 'CONEXIÓN DIRECTA';
        }

        // Proceso de infección simulado
        function startInfectionProcess() {
            const processes = [
                'proc1', 'proc2', 'proc3', 'proc4',
                'proc5', 'proc6', 'proc7', 'proc8'
            ];

            let currentProc = 0;

            const procInterval = setInterval(() => {
                if (currentProc < processes.length) {
                    // Completar proceso anterior
                    if (currentProc > 0) {
                        document.getElementById(processes[currentProc - 1]).classList.remove('active');
                        document.getElementById(processes[currentProc - 1]).classList.add('completed');
                    }

                    // Activar proceso actual
                    document.getElementById(processes[currentProc]).classList.add('active');
                    currentProc++;
                } else {
                    clearInterval(procInterval);
                }
            }, 1500);
        }

        // Contador regresivo final
        function startFinalCountdown() {
            let count = 15;
            const countdownElement = document.getElementById('countdown');

            const timer = setInterval(() => {
                count--;
                countdownElement.textContent = `FINALIZANDO ATAQUE EN: ${count}`;

                if (count <= 0) {
                    clearInterval(timer);
                    countdownElement.textContent = '🚨 ATAQUE COMPLETADO 🚨';
                    setTimeout(openMultipleTabs, 1000);
                }
            }, 1000);
        }

        // Abrir múltiples pestañas con sitios extraños
        function openMultipleTabs() {
            const weirdSites = [
                'https://www.youtube.com/watch?v=dQw4w9WgXcQ', // Rick Roll
                'https://www.zombo.com/',
                'https://www.staggeringbeauty.com/',
                'https://www.patience-is-a-virtue.org/',
                'https://www.koalastothemax.com/',
                'https://www.fallingfalling.com/',
                'https://www.cat-bounce.com/',
                'https://www.pointer-pointer.com/',
                'https://www.republiquedesmangues.fr/',
                'https://www.omfgdogs.com/',
                'https://www.hackertyper.net/',
                'https://www.windows93.net/',
                'https://www.theuselessweb.com/',
                'https://www.randomcolour.com/',
                'https://www.donothingfor2minutes.com/',
                'https://www.ismycomputeron.com/',
                'https://www.cant-not-tweet-this.com/',
                'https://www.ducksarethebest.com/',
                'https://www.beesbeesbees.com/',
                'https://www.endless.horse/'
            ];

            // Abrir pestañas con delay para evitar bloqueo del navegador
            let tabCount = 0;
            const maxTabs = Math.min(50, weirdSites.length * 3); // Máximo 50 pestañas

            const tabInterval = setInterval(() => {
                if (tabCount < maxTabs) {
                    const randomSite = weirdSites[Math.floor(Math.random() * weirdSites.length)];
                    try {
                        window.open(randomSite, '_blank');
                    } catch (e) {
                        console.log('Popup bloqueado');
                    }
                    tabCount++;
                } else {
                    clearInterval(tabInterval);
                }
            }, 200); // Abrir una pestaña cada 200ms
        }

        // Registrar visita en el servidor
        async function logVisit() {
            try {
                const response = await fetch('log_visit.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(userVisitData)
                });
            } catch (error) {
                console.log('Error al registrar visita');
            }
        }

        // Inicializar todo
        document.addEventListener('DOMContentLoaded', function() {
            initCustomCursor();
            createMatrixRain();
            startVirusLoading();

            // Efectos visuales adicionales
            setInterval(() => {
                const glitch = document.querySelector('.glitch');
                if (glitch) {
                    glitch.style.textShadow = `
                        ${Math.random() * 6 - 3}px ${Math.random() * 6 - 3}px 0 #ff0040,
                        ${Math.random() * 6 - 3}px ${Math.random() * 6 - 3}px 0 #00ffff,
                        0 0 20px #ff0040
                    `;
                }
            }, 100);
        });

        // Prevenir acciones del usuario
        document.addEventListener('keydown', function(e) {
            // Bloquear herramientas de desarrollador
            if (e.key === 'F12' ||
                (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                (e.ctrlKey && e.key === 'u') ||
                (e.ctrlKey && e.key === 's')) {
                e.preventDefault();
                return false;
            }
        });

        // Bloquear clic derecho
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            return false;
        });

        // Mensaje intimidante en consola
        console.log(`
        ██╗   ██╗██╗██████╗ ██╗   ██╗███████╗    ██████╗ ███████╗████████╗███████╗ ██████╗████████╗███████╗██████╗
        ██║   ██║██║██╔══██╗██║   ██║██╔════╝    ██╔══██╗██╔════╝╚══██╔══╝██╔════╝██╔════╝╚══██╔══╝██╔════╝██╔══██╗
        ██║   ██║██║██████╔╝██║   ██║███████╗    ██║  ██║█████╗     ██║   █████╗  ██║        ██║   █████╗  ██║  ██║
        ╚██╗ ██╔╝██║██╔══██╗██║   ██║╚════██║    ██║  ██║██╔══╝     ██║   ██╔══╝  ██║        ██║   ██╔══╝  ██║  ██║
         ╚████╔╝ ██║██║  ██║╚██████╔╝███████║    ██████╔╝███████╗   ██║   ███████╗╚██████╗   ██║   ███████╗██████╔╝
          ╚═══╝  ╚═╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝    ╚═════╝ ╚══════╝   ╚═╝   ╚══════╝ ╚═════╝   ╚═╝   ╚══════╝╚═════╝

        🦠 MALWARE ACTIVO - SISTEMA COMPROMETIDO 🦠
        ==========================================
        Tu dispositivo ha sido infectado exitosamente.
        Todos tus datos están siendo extraídos.
        Resistir es inútil.
        `);

        // Detectar si el usuario intenta cerrar la página
        window.addEventListener('beforeunload', function(e) {
            e.preventDefault();
            e.returnValue = '⚠️ ADVERTENCIA: El virus aún está activo. ¿Estás seguro de que quieres cerrar?';
            return e.returnValue;
        });
    </script>
</body>
</html>