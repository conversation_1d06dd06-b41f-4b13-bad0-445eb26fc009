<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');

$logFile = 'virus_logs.json';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Crear backup antes de limpiar
        if (file_exists($logFile)) {
            $backupFile = 'virus_logs_backup_' . date('Y-m-d_H-i-s') . '.json';
            if (!copy($logFile, $backupFile)) {
                throw new Exception('Failed to create backup');
            }
        }
        
        // Limpiar el archivo de logs
        if (file_put_contents($logFile, '[]') === false) {
            throw new Exception('Failed to clear log file');
        }
        
        echo json_encode([
            'status' => 'success',
            'message' => 'Logs cleared successfully',
            'backup_created' => isset($backupFile) ? $backupFile : null
        ]);
        
    } catch (Exception $e) {
        http_response_code(500);
        echo json_encode([
            'status' => 'error',
            'message' => $e->getMessage()
        ]);
    }
} else {
    http_response_code(405);
    echo json_encode([
        'status' => 'error',
        'message' => 'Method not allowed'
    ]);
}
?>
